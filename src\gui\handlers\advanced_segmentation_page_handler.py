
# src/gui/handlers/advanced_segmentation_handler.py

import logging
import os
import time
import cv2
import numpy as np
import torch
import pickle
import json
from datetime import datetime
from pathlib import Path

from PySide6.QtCore import Qt, QPoint, Signal, Slot, QRectF, QEvent
from PySide6.QtGui import QPainter, QPen, QColor, QPixmap, QImage, QMouseEvent, QCursor, QKeyEvent
from PySide6.QtWidgets import (QPushButton, QComboBox, QSlider, QSpinBox, QCheckBox,
                              QMessageBox, QFileDialog, QVBoxLayout, QHBoxLayout,
                              QListWidgetItem, QMenu, QProgressDialog)

from src.segmentation.sam_segmentation_handler import SAMSegmentationHandler
from src.utils.image_utils import convert_cvimage_to_qpixmap, convert_cvimage_to_qimage # Assuming these are correctly defined elsewhere
from src.utils.session_state import SessionState  # Still needed for session_state parameter

logger = logging.getLogger(__name__)

# Placeholder for image_utils functions if not fully provided in prompt
# You should have these defined correctly in your project structure
# ==============================================================
# src/utils/image_utils.py
def convert_cvimage_to_qimage(cv_image, already_rgb=False):
    """Converts an OpenCV image to a QImage."""
    if cv_image is None:
        return QImage()
    try:
        if len(cv_image.shape) == 3 and cv_image.shape[2] == 3 and not already_rgb:
            cv_image_rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        elif len(cv_image.shape) == 2: # Grayscale
             cv_image_rgb = cv2.cvtColor(cv_image, cv2.COLOR_GRAY2RGB)
        else:
            cv_image_rgb = cv_image

        # Ensure data is contiguous
        cv_image_rgb = np.ascontiguousarray(cv_image_rgb)

        h, w, ch = cv_image_rgb.shape
        bytes_per_line = ch * w
        q_img = QImage(cv_image_rgb.data, w, h, bytes_per_line, QImage.Format_RGB888)
        return q_img.copy() # Return a copy to avoid issues with data ownership
    except Exception as e:
        logger.error(f"Error converting CV image to QImage: {e}")
        return QImage()

def convert_cvimage_to_qpixmap(cv_image, already_rgb=False):
    """Converts an OpenCV image to QPixmap."""
    q_image = convert_cvimage_to_qimage(cv_image, already_rgb)
    if q_image.isNull():
        return QPixmap()
    return QPixmap.fromImage(q_image)
# ==============================================================


class AdvancedSegmentationPageHandler:
    """Handler for the Trainable Segmentation page functionality."""

    def __init__(self, ui, session_state=None):
        self.ui = ui
        # Initialize SAM handler with lazy initialization
        self.sam_handler = SAMSegmentationHandler(lazy_init=True)

        # Image state
        self.current_image = None       # Holds the loaded OpenCV image (BGR)
        self.current_image_path = None  # Holds the CANONICAL (relative) path of the current image
        self.current_image_index = -1   # Index in self.image_paths
        self.image_paths = []           # List of CANONICAL (relative) image paths
        self.image_infos = []           # List of image info dicts (potentially unused, consider removing if not needed)

        # Annotation state
        # Dictionary storing annotations: {canonical_image_path: [annotation_objects]}
        self.annotations = {}
        self.current_annotation = None # Temporary annotation being drawn (unused currently)
        self.current_tool = None
        self.drawing = False
        self.points = [] # Points for polygon tool in progress

        # Class state
        self.current_class = 1
        self.class_names = {1: "Class 1", 2: "Class 2", 3: "Class 3"}  # Default class names
        self.class_colors = {
            1: QColor(255, 0, 0),    # Red
            2: QColor(0, 255, 0),    # Green
            3: QColor(0, 0, 255)     # Blue
        }

        # Brush and erase state
        self.brush_size = 10
        self.brush_mask = None  # Current temporary mask being drawn by brush
        self.last_brush_point = None

        # MobileSAM state
        self.drawing_sam_bbox = False
        self.start_point = None # BBox start point
        self.end_point = None   # BBox end point / Current rectangle point
        self.current_mask = None # Mask generated by SAM/Brush/Magic Wand before accepting
        self.point_prompts = []  # List of (x, y, label) for SAM point prompts
        self.is_adding_points = False # Flag for SAM multi-point mode

        # Editing state
        self.editing_mode = False
        self.selected_annotation_index = None # Index in the image's annotation list
        self.editing_points = [] # Control points of the annotation being edited
        self.selected_point_index = None # Index of the control point being dragged
        self.hover_point_index = None    # Index of the control point being hovered over
        self.drag_point = False          # Flag indicating a control point is being dragged

        # Flag to track deferred annotation updates for the erase tool
        self._annotations_modified = False

        # Storage for annotations of images not yet loaded
        self.pending_annotations = {}

        # Session state
        self.session_state = session_state
        if not self.session_state and hasattr(ui, 'session_state'):
            self.session_state = ui.session_state

        if self.session_state:
            # Ensure project_dir is set if possible (might be deferred)
            if not self.session_state.project_dir and hasattr(self.ui, 'project') and self.ui.project:
                project = self.ui.project
                proj_path = getattr(project, 'project_file', getattr(project, 'project_dir', None))
                if proj_path:
                    self.session_state.set_project_dir(proj_path)

            if self.session_state.state_dir:
                logger.info(f"Using session state with directory: {self.session_state.state_dir}")
            else:
                logger.warning("Session state initialized but no state directory set (might be set later)")
        else:
             logger.warning("Session state not provided or found in UI.")

        # Setup handlers
        self.setup_handlers()

        # Initialize UI state
        self.update_class_selector()
        self.ui.accept_sam_btn.setEnabled(False)
        self.ui.reject_sam_btn.setEnabled(False)
        self.ui.reset_points_btn.setEnabled(False)
        self.ui.accept_sam_btn.setToolTip("Accept mask (Enter)")
        self.ui.reject_sam_btn.setToolTip("Reject mask (Escape)")
        self.ui.reset_points_btn.setToolTip("Reset all points (R)")

        # Initialize Refine Mask with SAM button (disabled by default)
        self.ui.refine_mask_sam_btn.setEnabled(False)
        self.ui.refine_mask_sam_btn.setToolTip("Run Model Inference first to enable this feature")

        # Synchronize image views
        self.synchronize_image_views()

    # --- Path Management Helpers ---

    def _get_project_images_dir(self):
        """Gets the absolute path to the project's images directory."""
        if not self.session_state or not self.session_state.project_dir:
            logger.error("Cannot determine image directory: Session state or project directory not set.")
            return None

        project_dir = self.session_state.project_dir
        images_dir = None

        # Check for VisionLabProject structure (.vlp file)
        if str(project_dir).lower().endswith('.vlp'):
            project_name = Path(project_dir).stem
            data_dir = Path(project_dir).parent / f"{project_name}_data"
            images_dir = data_dir / "images"
        # Check if project_dir is the VisionLab data directory
        elif Path(project_dir).name.endswith('_data'):
             vlp_file = Path(project_dir).parent / f"{Path(project_dir).stem.replace('_data', '')}.vlp"
             if vlp_file.exists():
                 images_dir = Path(project_dir) / "images"
        # Assume regular project structure
        else:
             # Check for new_project_data directory first (specific to your project structure)
             new_project_data_dir = Path(project_dir) / "new_project_data"
             if new_project_data_dir.exists():
                 images_dir = new_project_data_dir / "images"
                 if images_dir.exists():
                     logger.info(f"Found images directory in new_project_data: {images_dir}")
                     return str(images_dir.resolve())

             # Check if a corresponding _data directory exists
             project_name = Path(project_dir).name
             data_dir = Path(project_dir) / f"{project_name}_data"
             state_dir_in_data = data_dir / "state" / "advanced_segmentation"
             if data_dir.exists() and state_dir_in_data.exists():
                 images_dir = data_dir / "images"
             else:
                 # Fallback to images subdir directly inside project_dir
                 images_dir_direct = Path(project_dir) / "images"
                 if images_dir_direct.exists():
                     images_dir = images_dir_direct
                 else:
                     # As a last resort, assume the project_dir *is* the images dir if it contains image files
                     if any(f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tif', '.tiff')) for f in os.listdir(project_dir)):
                         images_dir = Path(project_dir)
                     else:
                         # Default to standard "images" subdir even if it doesn't exist yet
                         images_dir = Path(project_dir) / "images"


        if images_dir:
            # Ensure the directory exists
            try:
                 os.makedirs(images_dir, exist_ok=True)
                 return str(images_dir.resolve())
            except OSError as e:
                 logger.error(f"Could not create or access images directory {images_dir}: {e}")
                 return None
        else:
            logger.error(f"Could not determine images directory for project: {project_dir}")
            return None

    def _get_canonical_path(self, absolute_path):
        """Converts an absolute image path to its canonical relative path."""
        images_dir = self._get_project_images_dir()
        if not images_dir:
            logger.warning(f"Cannot get canonical path for {absolute_path}: Image directory unknown.")
            # Fallback: return the absolute path using forward slashes
            return Path(absolute_path).as_posix()

        try:
            # Ensure both paths are resolved and absolute for comparison
            abs_path_obj = Path(absolute_path).resolve()
            images_dir_obj = Path(images_dir).resolve()

            # Check if the image path is inside the images directory
            if images_dir_obj in abs_path_obj.parents or images_dir_obj == abs_path_obj.parent:
                 relative_path = abs_path_obj.relative_to(images_dir_obj)
                 # Return path with forward slashes
                 return relative_path.as_posix()
            else:
                 logger.warning(f"Image path {absolute_path} is not within the project images directory {images_dir}.")
                 # Fallback: return filename only
                 return Path(absolute_path).name

        except ValueError:
            logger.warning(f"Could not determine relative path for {absolute_path} based on {images_dir}.")
             # Fallback: return filename only
            return Path(absolute_path).name
        except Exception as e:
            logger.error(f"Error getting canonical path for {absolute_path}: {e}")
             # Fallback: return filename only
            return Path(absolute_path).name


    def _get_absolute_path(self, canonical_path):
        """Converts a canonical relative path back to an absolute path."""
        images_dir = self._get_project_images_dir()
        if not images_dir:
            logger.warning(f"Cannot get absolute path for {canonical_path}: Image directory unknown.")
            # Attempt to return the canonical path itself, it might be absolute already in fallback cases
            return canonical_path

        # Handle cases where canonical_path might have become absolute due to errors
        if os.path.isabs(canonical_path):
             return canonical_path

        abs_path = Path(images_dir) / canonical_path
        return str(abs_path.resolve())

    def _get_image_annotation_dir_name(self, canonical_path):
        """Gets the safe directory name for storing NPZ files for a given image."""
        # Use the filename from the canonical path
        image_filename = Path(canonical_path).name
        # Replace dots with underscores for directory name safety
        safe_name = image_filename.replace('.', '_')
        logger.debug(f"Created safe directory name '{safe_name}' for image '{image_filename}'")
        return safe_name

    def _get_image_path_from_dir_name(self, dir_name):
        """Reverse of _get_image_annotation_dir_name - converts directory name back to image filename.
        This is needed to match directory names to image paths when loading annotations."""
        # This is a heuristic - we assume the directory name was created by replacing dots with underscores
        # We can't know for sure which underscores were originally dots, so we try to match with existing images

        # First, try the simple case - maybe the directory name is already the image filename
        for img_path in self.image_paths:
            img_filename = Path(img_path).name
            if dir_name == img_filename:
                return img_path

        # Next, try replacing underscores with dots to see if we get a match
        for img_path in self.image_paths:
            img_filename = Path(img_path).name
            # Create the safe name and check if it matches the directory name
            safe_name = img_filename.replace('.', '_')
            if dir_name == safe_name:
                logger.debug(f"Matched directory name '{dir_name}' to image '{img_filename}'")
                return img_path

        # If we still don't have a match, try a more aggressive approach
        # This is a fallback for cases where the directory name might have been created differently
        for img_path in self.image_paths:
            img_filename = Path(img_path).name
            img_name_no_ext = Path(img_path).stem

            # Check if the directory name starts with the image name without extension
            if dir_name.startswith(img_name_no_ext):
                logger.debug(f"Matched directory name '{dir_name}' to image '{img_filename}' using name prefix")
                return img_path

        logger.warning(f"Could not match directory name '{dir_name}' to any loaded image")
        return None

    def _get_adv_seg_state_dir(self):
        """Gets the absolute path to the advanced_segmentation state directory."""
        if self.session_state and self.session_state.state_dir:
            adv_seg_dir = Path(self.session_state.state_dir) / 'advanced_segmentation'
            os.makedirs(adv_seg_dir, exist_ok=True)
            return str(adv_seg_dir)
        logger.warning("Cannot get trainable segmentation state dir: Session state not configured.")
        return None

    # --- End Path Management Helpers ---

    def synchronize_image_views(self):
        """Synchronize the annotation and segmentation results views."""
        if hasattr(self.ui, 'annotation_image_view') and hasattr(self.ui, 'segmentation_results_view'):
            self.ui.annotation_image_view.add_synced_view(self.ui.segmentation_results_view)
            self.ui.segmentation_results_view.add_synced_view(self.ui.annotation_image_view)
        else:
            logger.warning("Could not synchronize image views: UI elements not found.")


    def setup_handlers(self):
        """Set up event handlers for the UI components."""
        # Use partial or lambda to avoid late binding issues in loops if needed
        self.ui.select_tool_btn.clicked.connect(lambda: self.set_current_tool("select"))
        self.ui.polygon_tool_btn.clicked.connect(lambda: self.set_current_tool("polygon"))
        self.ui.rectangle_tool_btn.clicked.connect(lambda: self.set_current_tool("rectangle"))
        self.ui.point_prompt_tool_btn.clicked.connect(self.toggle_point_prompt)
        self.ui.negative_point_prompt_tool_btn.clicked.connect(self.toggle_negative_point_prompt)
        self.ui.brush_tool_btn.clicked.connect(lambda: self.set_current_tool("brush"))
        self.ui.erase_tool_btn.clicked.connect(lambda: self.set_current_tool("erase"))
        self.ui.magic_wand_tool_btn.clicked.connect(lambda: self.set_current_tool("magic_wand"))

        self.ui.brush_size_slider.valueChanged.connect(self.on_brush_size_changed)
        self.brush_size = self.ui.brush_size_slider.value()
        self.ui.brush_size_value_label.setText(str(self.brush_size))

        self.ui.accept_sam_btn.clicked.connect(self.accept_mask)
        self.ui.reject_sam_btn.clicked.connect(self.reject_mask)
        self.ui.reset_points_btn.clicked.connect(self.reset_points)

        self.ui.edit_annotation_btn.clicked.connect(self.edit_annotation)
        self.ui.remove_annotation_btn.clicked.connect(self.remove_annotation)
        self.ui.clear_all_annotations_btn.clicked.connect(self.clear_all_annotations)

        # Connect save and load annotations buttons
        self.ui.save_annotations_btn.clicked.connect(self.save_annotations_to_file)
        self.ui.load_annotations_btn.clicked.connect(self.load_annotations_from_file)
        
        # Connect Quick Save and Quick Load buttons
        self.ui.quick_save_annotations_btn.clicked.connect(self.quick_save_annotations)
        self.ui.quick_load_annotations_btn.clicked.connect(self.quick_load_annotations)

        # Connect the selection changed signal to update the display
        self.ui.annotations_list.itemSelectionChanged.connect(self.on_annotation_selection_changed)

        self.ui.class_selector.currentIndexChanged.connect(self.on_class_selected)
        self.ui.manage_classes_btn.clicked.connect(self.show_class_management_dialog)

        self.ui.dataset_preparation_btn.clicked.connect(self.show_dataset_preparation_dialog)
        self.ui.import_dataset_btn.clicked.connect(self.import_dataset)

        self.ui.model_trainer_btn.clicked.connect(self.show_model_trainer_dialog)
        self.ui.model_inference_btn.clicked.connect(self.run_model_inference)
        self.ui.model_evaluation_btn.clicked.connect(self.show_model_evaluation_dialog)
        self.ui.refine_mask_sam_btn.clicked.connect(self.refine_mask_with_sam)
        
        # Connect clear advanced segmentation gallery button
        if hasattr(self.ui, 'clear_adv_seg_gallery_button'):
            self.ui.clear_adv_seg_gallery_button.clicked.connect(self.clear_advanced_segmentation_gallery)

        # Connect mouse/keyboard events for the annotation view
        if hasattr(self.ui, 'annotation_image_view'):
            self.ui.annotation_image_view.mousePressEvent = self.on_annotation_view_mouse_press
            self.ui.annotation_image_view.mouseMoveEvent = self.on_annotation_view_mouse_move
            self.ui.annotation_image_view.mouseReleaseEvent = self.on_annotation_view_mouse_release
            self.ui.annotation_image_view.keyPressEvent = self.on_key_press
            self.ui.annotation_image_view.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        else:
            logger.error("UI missing 'annotation_image_view', cannot connect events.")


    def set_current_tool(self, tool_name):
        """Set the current annotation tool, handling toggling."""
        logger.debug(f"Request to set tool: {tool_name}, Current tool: {self.current_tool}")

        # --- Deactivation Logic ---
        if tool_name == self.current_tool:
            logger.info(f"Deactivating tool: {tool_name}")
            # Uncheck all tool buttons
            for btn in [self.ui.select_tool_btn, self.ui.polygon_tool_btn, self.ui.rectangle_tool_btn,
                       self.ui.point_prompt_tool_btn, self.ui.negative_point_prompt_tool_btn,
                       self.ui.brush_tool_btn, self.ui.erase_tool_btn,
                       self.ui.magic_wand_tool_btn]:
                btn.setChecked(False)

            # Specific cleanup for deactivated tool
            if self.current_tool in ["point_prompt", "negative_point_prompt"]:
                self.is_adding_points = False # Keep prompts, just exit adding mode
            if self.current_tool == "select":
                 self.exit_editing_mode() # Clean up selection state
            if self.current_tool == "erase" and self._annotations_modified:
                self._save_state_after_erase() # Save pending erase changes

            # Reset common states
            self.drawing = False
            self.drawing_sam_bbox = False
            self.points = []
            self.start_point = None
            self.end_point = None
            self.last_brush_point = None
            self.current_tool = None
            self.ui.annotation_image_view.setCursor(Qt.CursorShape.ArrowCursor)
            self.update_annotation_display()
            logger.info("No tool active")
            if hasattr(self.ui, 'statusBar'): self.ui.statusBar().clearMessage()
            return

        # --- Activation Logic ---
        logger.info(f"Activating tool: {tool_name}")
        previous_tool = self.current_tool

        # Uncheck all buttons first
        for btn in [self.ui.select_tool_btn, self.ui.polygon_tool_btn, self.ui.rectangle_tool_btn,
                   self.ui.point_prompt_tool_btn, self.ui.negative_point_prompt_tool_btn,
                   self.ui.brush_tool_btn, self.ui.erase_tool_btn,
                   self.ui.magic_wand_tool_btn]:
            btn.setChecked(False)

        # Specific cleanup when switching *away* from certain tools
        if previous_tool in ["point_prompt", "negative_point_prompt"] and tool_name not in ["point_prompt", "negative_point_prompt"]:
            self.is_adding_points = False # Keep points but exit mode
        if previous_tool == "select" and tool_name != "select":
            self.exit_editing_mode()
        if previous_tool == "erase" and tool_name != "erase" and self._annotations_modified:
             self._save_state_after_erase()

        # Set the new tool and update UI
        self.current_tool = tool_name
        cursor = Qt.CursorShape.ArrowCursor
        tool_display_name = ""

        if tool_name == "select":
            self.ui.select_tool_btn.setChecked(True)
            self.exit_editing_mode() # Reset selection state on activation
            cursor = Qt.CursorShape.ArrowCursor
            tool_display_name = "Select (S)"
        elif tool_name == "polygon":
            self.ui.polygon_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.CrossCursor
            tool_display_name = "Polygon"
        elif tool_name == "rectangle":
            self.ui.rectangle_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.CrossCursor
            tool_display_name = "Rectangle"
        elif tool_name == "point_prompt":
            self.ui.point_prompt_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.CrossCursor
            self.is_adding_points = True # Enter point adding mode
            tool_display_name = "Positive Point (P)"
        elif tool_name == "negative_point_prompt":
            self.ui.negative_point_prompt_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.CrossCursor
            self.is_adding_points = True # Enter point adding mode
            tool_display_name = "Negative Point (N)"
        elif tool_name == "brush":
            self.ui.brush_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.PointingHandCursor
            if self.current_image is not None and self.brush_mask is None:
                self.brush_mask = np.zeros(self.current_image.shape[:2], dtype=np.uint8)
            tool_display_name = "Brush (B)"
        elif tool_name == "erase":
            self.ui.erase_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.PointingHandCursor
            # Eraser operates on existing annotations, no separate brush_mask needed conceptually
            self._annotations_modified = False # Reset modification flag
            tool_display_name = "Erase (E)"
        elif tool_name == "magic_wand":
            self.ui.magic_wand_tool_btn.setChecked(True)
            cursor = Qt.CursorShape.CrossCursor
            tool_display_name = "Magic Wand (Box)"
        else: # Should not happen if called correctly
             self.current_tool = None
             logger.error(f"Unknown tool name requested: {tool_name}")

        # Reset common drawing states
        self.drawing = False
        self.drawing_sam_bbox = False
        self.points = []
        self.start_point = None
        self.end_point = None
        self.last_brush_point = None

        self.ui.annotation_image_view.setCursor(cursor)
        self.update_annotation_display()
        logger.info(f"Current tool set to: {tool_display_name if tool_display_name else 'None'}")
        if hasattr(self.ui, 'statusBar') and tool_display_name:
            self.ui.statusBar().showMessage(f"Tool: {tool_display_name}", 3000)

    def _save_state_after_erase(self):
        """Helper to reload annotations after erasing."""
        if self._annotations_modified:
            logger.info("Updating annotations after erase tool modifications.")
            self.load_annotations() # Update list widget
            # No longer automatically saving state
            self._annotations_modified = False # Reset flag

    def exit_editing_mode(self):
        """Exit the annotation editing mode."""
        if not self.editing_mode:
            return
        self.editing_mode = False
        self.selected_annotation_index = None
        self.editing_points = []
        self.selected_point_index = None
        self.hover_point_index = None
        self.drag_point = False
        self.update_annotation_display()
        # Reset cursor if not hovering over another annotation
        pos = self.ui.annotation_image_view.mapFromGlobal(QCursor.pos())
        scene_pos = self.ui.annotation_image_view.mapToScene(pos)
        if self.find_annotation_at_point(int(scene_pos.x()), int(scene_pos.y())) is None:
             self.ui.annotation_image_view.setCursor(Qt.CursorShape.ArrowCursor)

        logger.info("Exited annotation editing mode")

    def on_brush_size_changed(self, value):
        """Handle brush size slider value change."""
        self.brush_size = value
        self.ui.brush_size_value_label.setText(str(value))
        # No need to log verbosely unless debugging
        # logger.info(f"Brush size set to: {value}")

    def toggle_point_prompt(self):
        """Toggle the positive point prompt tool."""
        if self.current_tool == "point_prompt":
            self.set_current_tool("point_prompt") # Will deactivate
        else:
            self.set_current_tool("point_prompt") # Will activate
        self.ui.reset_points_btn.setEnabled(len(self.point_prompts) > 0)

    def toggle_negative_point_prompt(self):
        """Toggle the negative point prompt tool."""
        if self.current_tool == "negative_point_prompt":
            self.set_current_tool("negative_point_prompt") # Will deactivate
        else:
            self.set_current_tool("negative_point_prompt") # Will activate
        self.ui.reset_points_btn.setEnabled(len(self.point_prompts) > 0)


    def load_images_from_project_hub(self, image_paths, image_infos):
        """Load images provided as absolute paths."""
        logger.info(f"Attempting to load {len(image_paths)} images from project hub.")
        if not image_paths:
            logger.warning("No image paths provided to load.")
            return

        # Initialize pending_annotations if it doesn't exist
        if not hasattr(self, 'pending_annotations'):
            self.pending_annotations = {}

        new_canonical_paths = []
        new_infos_ordered = []
        processed_abs_paths = set(self._get_absolute_path(cp) for cp in self.image_paths)

        for abs_path, info in zip(image_paths, image_infos):
            if not os.path.exists(abs_path):
                logger.warning(f"Skipping non-existent image file: {abs_path}")
                continue

            # Prevent adding duplicates based on resolved absolute path
            resolved_abs_path = str(Path(abs_path).resolve())
            if resolved_abs_path in processed_abs_paths:
                logger.info(f"Skipping duplicate image: {abs_path}")
                continue

            canonical_path = self._get_canonical_path(abs_path)
            if canonical_path:
                new_canonical_paths.append(canonical_path)
                new_infos_ordered.append(info)
                processed_abs_paths.add(resolved_abs_path) # Add resolved path to prevent duplicates

                # Check if there are pending annotations for this image
                if canonical_path in self.pending_annotations:
                    logger.info(f"Found pending annotations for {canonical_path}")
                    self.annotations[canonical_path] = self.pending_annotations[canonical_path]
                    del self.pending_annotations[canonical_path]
                else:
                    # Try to match by filename
                    filename = Path(canonical_path).name
                    for pending_path in list(self.pending_annotations.keys()):
                        if Path(pending_path).name == filename:
                            logger.info(f"Matched pending annotations by filename for: {canonical_path}")
                            self.annotations[canonical_path] = self.pending_annotations[pending_path]
                            del self.pending_annotations[pending_path]
                            break
            else:
                logger.warning(f"Could not get canonical path for: {abs_path}")

        if not new_canonical_paths:
            logger.info("No new images were added (all were duplicates or invalid).")
            return

        # Append new canonical paths and infos
        self.image_paths.extend(new_canonical_paths)
        self.image_infos.extend(new_infos_ordered) # Assuming order matches

        logger.info(f"Added {len(new_canonical_paths)} new images.")

        # Rebuild the gallery entirely to reflect the new list
        self.rebuild_gallery()

        # If no image was previously selected, select the first one added
        if self.current_image_index < 0 and self.image_paths:
             logger.info("No image selected, selecting the first image.")
             self.select_image(0) # Select the very first image in the updated list

        # Save state to ensure all annotations are properly saved
        self.save_state()


    def clear_gallery(self):
        """Clear the image gallery UI."""
        self.ui.adv_seg_gallery.clear_images() # Use gallery's method

    def add_image_to_gallery(self, absolute_path, canonical_path):
        """Add a single image thumbnail to the gallery UI."""
        try:
            img = cv2.imread(absolute_path)
            if img is None:
                logger.error(f"Failed to load image for gallery thumbnail: {absolute_path}")
                # Add a placeholder or skip? Skipping for now.
                return

            filename = Path(canonical_path).name # Display filename from canonical path

            # Gallery expects RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Add to the gallery UI widget
            self.ui.adv_seg_gallery.add_image(img_rgb, filename, canonical_path) # Store canonical path

            # Ensure signal connections (idempotent connection is fine)
            try:
                self.ui.adv_seg_gallery.image_clicked.disconnect(self.select_image)
            except (TypeError, RuntimeError): # Catch if not connected
                pass
            self.ui.adv_seg_gallery.image_clicked.connect(self.select_image)

            # Connect remove button for the *last added* thumbnail
            if self.ui.adv_seg_gallery.thumbnails:
                 thumb = self.ui.adv_seg_gallery.thumbnails[-1]
                 try:
                      thumb.remove_clicked.disconnect(self.remove_image_from_gallery)
                 except (TypeError, RuntimeError):
                      pass
                 thumb.remove_clicked.connect(self.remove_image_from_gallery)

        except Exception as e:
            logger.error(f"Error adding image {absolute_path} to gallery: {str(e)}")

    def rebuild_gallery(self):
        """Rebuild the gallery UI from scratch using the current `self.image_paths`."""
        logger.info(f"Rebuilding gallery with {len(self.image_paths)} images.")

        current_canonical_path = self.current_image_path # Store currently selected canonical path

        self.clear_gallery() # Clear the UI

        # Reset the gallery's internal state (important!)
        self.ui.adv_seg_gallery.images = []
        self.ui.adv_seg_gallery.thumbnails = []
        self.ui.adv_seg_gallery.filenames = []
        self.ui.adv_seg_gallery.file_paths = [] # Gallery stores its own paths
        self.ui.adv_seg_gallery.selected_index = -1
        logger.debug("Cleared gallery internal state")

        # Add all images back based on self.image_paths (canonical)
        for i, canonical_path in enumerate(self.image_paths):
            absolute_path = self._get_absolute_path(canonical_path)
            if not os.path.exists(absolute_path):
                 logger.warning(f"Skipping missing image during gallery rebuild: {absolute_path} (Canonical: {canonical_path})")
                 # Consider removing this path from self.image_paths? For now, skip adding to gallery.
                 continue
            self.add_image_to_gallery(absolute_path, canonical_path)

        logger.info(f"Gallery rebuild populated with {len(self.ui.adv_seg_gallery.thumbnails)} thumbnails.")

        # Reselect the previously selected image, if it still exists
        new_index = -1
        if current_canonical_path and current_canonical_path in self.image_paths:
            try:
                new_index = self.image_paths.index(current_canonical_path)
                logger.info(f"Found previously selected image '{current_canonical_path}' at new index {new_index}.")
            except ValueError: # Should not happen if check above passed, but safety first
                logger.warning(f"Previously selected image '{current_canonical_path}' not found in list after rebuild.")
                new_index = -1
        elif self.image_paths: # If no previous selection or it's gone, select the first image
             new_index = 0
             logger.info("No previous selection or it was removed, selecting first image.")

        # Perform the selection
        if new_index != -1:
            self.select_image(new_index)
            # Ensure gallery UI also highlights the correct thumbnail
            self.ui.adv_seg_gallery.select_image(new_index)
        else:
            # No images left or something went wrong
            self.current_image_index = -1
            self.current_image_path = None
            self.current_image = None
            self.ui.annotation_image_view.set_pixmap(QPixmap())
            self.ui.annotations_list.clear()
            self.ui.segmentation_results_view.set_pixmap(QPixmap())
            logger.info("No images to select after gallery rebuild.")

    def remove_image_from_gallery(self, index):
        """Remove an image by its index in the gallery/self.image_paths."""
        if not (0 <= index < len(self.image_paths)):
            logger.error(f"Invalid image index for removal: {index}")
            return

        canonical_path_to_remove = self.image_paths[index]
        filename = Path(canonical_path_to_remove).name

        reply = QMessageBox.question(
            self.ui, "Confirm Removal",
            f"Are you sure you want to remove this image and its annotations?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply != QMessageBox.StandardButton.Yes:
            return

        logger.info(f"Removing image at index {index}: {canonical_path_to_remove}")

        # Save state *before* removal
        if self.current_image_path:
            self.save_state()

        # Store if the removed image was the current one
        was_current = (self.current_image_index == index)

        # Remove from internal lists
        del self.image_paths[index]
        if index < len(self.image_infos): # Safety check
            del self.image_infos[index]

        # Remove annotations for this canonical path
        if canonical_path_to_remove in self.annotations:
            del self.annotations[canonical_path_to_remove]
            logger.info(f"Removed annotations for {canonical_path_to_remove}")
            # Also attempt to remove the NPZ directory
            self._remove_image_annotation_dir(canonical_path_to_remove)


        # --- Crucial: Adjust current_image_index BEFORE rebuilding ---
        if was_current:
            # If the removed image was the current one, select the next one or previous one
            if index < len(self.image_paths):
                # Select the image that is now at the removed index (the one after)
                self.current_image_index = index
            elif self.image_paths:
                # Select the new last image
                self.current_image_index = len(self.image_paths) - 1
            else:
                # No images left
                self.current_image_index = -1
            self.current_image_path = self.image_paths[self.current_image_index] if self.current_image_index != -1 else None
        elif self.current_image_index > index:
             # If an image before the current one was removed, decrement the index
            self.current_image_index -= 1
            self.current_image_path = self.image_paths[self.current_image_index] if self.current_image_index != -1 else None


        # Rebuild the gallery to reflect changes and handle selection visually
        self.rebuild_gallery() # This will call select_image internally if needed

        # Save state *after* removal and selection update
        self.save_state()
        logger.info(f"Image removal complete. Current index: {self.current_image_index}")

    def _remove_image_annotation_dir(self, canonical_path):
        """Removes the NPZ storage directory for a given image."""
        adv_seg_state_dir = self._get_adv_seg_state_dir()
        if not adv_seg_state_dir: return

        dir_name = self._get_image_annotation_dir_name(canonical_path)
        dir_path = Path(adv_seg_state_dir) / dir_name

        if dir_path.is_dir():
            try:
                import shutil
                shutil.rmtree(dir_path)
                logger.info(f"Removed annotation directory: {dir_path}")
            except Exception as e:
                logger.error(f"Error removing annotation directory {dir_path}: {e}")
        else:
             logger.warning(f"Annotation directory not found for removal: {dir_path}")


    def select_image(self, index):
        """Select an image by index."""
        if not (0 <= index < len(self.image_paths)):
            logger.error(f"Invalid image index requested for selection: {index}")
            # Attempt to recover by selecting the first image if available
            if self.image_paths:
                 logger.warning("Attempting to select index 0 instead.")
                 index = 0
            else:
                 logger.error("No images available to select.")
                 # Clear everything if no images are left
                 self.current_image_index = -1
                 self.current_image_path = None
                 self.current_image = None
                 self.ui.annotation_image_view.set_pixmap(QPixmap())
                 self.ui.annotations_list.clear()
                 self.ui.segmentation_results_view.set_pixmap(QPixmap())
                 return

        # No longer automatically saving state when switching images
        # State will be saved manually by the user


        # Update selection state
        self.current_image_index = index
        self.current_image_path = self.image_paths[index] # Get the canonical path

        logger.info(f"Selected image index: {index}, Canonical Path: {self.current_image_path}")

        # Get absolute path for loading
        absolute_path = self._get_absolute_path(self.current_image_path)

        if not os.path.exists(absolute_path):
            logger.error(f"Image file not found at absolute path: {absolute_path} (derived from: {self.current_image_path})")
            QMessageBox.critical(self.ui, "Error", f"Image file not found:\n{absolute_path}\nPlease check the project structure.")
            # Clear display and potentially remove the image from the list?
            self.current_image = None
            self.ui.annotation_image_view.set_pixmap(QPixmap())
            self.ui.annotations_list.clear()
            # Optionally remove the broken link here
            # self.remove_image_from_gallery(index) # Be careful with recursion
            return

        # Load the image file
        if not self.load_image(absolute_path):
            # load_image will show its own error message
            self.current_image = None
            self.ui.annotation_image_view.set_pixmap(QPixmap())
            self.ui.annotations_list.clear()
            return

        # Check if we already have annotations for this image
        if self.current_image_path not in self.annotations:
            # Check if there are pending annotations for this image
            if hasattr(self, 'pending_annotations') and self.current_image_path in self.pending_annotations:
                logger.info(f"Found pending annotations for {self.current_image_path}")
                self.annotations[self.current_image_path] = self.pending_annotations[self.current_image_path]
                del self.pending_annotations[self.current_image_path]
            elif hasattr(self, 'pending_annotations'):
                # Try to match by filename
                filename = Path(self.current_image_path).name
                for pending_path in list(self.pending_annotations.keys()):
                    if Path(pending_path).name == filename:
                        logger.info(f"Matched pending annotations by filename for: {self.current_image_path}")
                        self.annotations[self.current_image_path] = self.pending_annotations[pending_path]
                        del self.pending_annotations[pending_path]
                        break

        # Load annotations for the selected canonical path
        self.load_annotations() # Uses self.current_image_path

        # Update the gallery UI selection
        self.ui.adv_seg_gallery.select_image(index)

        # Update display (redundant call often, but ensures consistency)
        self.update_annotation_display()

        # Display segmentation preview if available
        self.display_current_segmentation_preview()

    def load_image(self, absolute_path):
        """Load image from absolute path into viewer and SAM handler."""
        logger.debug(f"Loading image file: {absolute_path}")
        try:
            img = cv2.imread(absolute_path)
            if img is None:
                raise IOError(f"cv2.imread returned None for {absolute_path}. File might be corrupted or format unsupported.")

            self.current_image = img # Store the BGR image

            # Display in annotation view (needs RGB)
            pixmap = convert_cvimage_to_qpixmap(self.current_image, already_rgb=False)
            if pixmap.isNull():
                 raise ValueError("Failed to convert OpenCV image to QPixmap.")

            # Fit image to view when loading a new one
            self.ui.annotation_image_view.set_pixmap(pixmap, preserve_zoom=False, is_brush_update=False)

            # Set image in SAM handler
            self.sam_handler.set_image(self.current_image)

            # Reset tool-specific states that depend on the image
            self.reset_tool_states()

            logger.info(f"Successfully loaded image: {Path(absolute_path).name}")
            return True

        except Exception as e:
            logger.error(f"Error loading image {absolute_path}: {str(e)}")
            QMessageBox.critical(self.ui, "Error Loading Image", f"Failed to load image:\n{absolute_path}\n\nError: {str(e)}")
            return False

    def reset_tool_states(self):
         """Reset states related to tools when a new image is loaded."""
         self.drawing = False
         self.points = []
         self.brush_mask = None
         self.last_brush_point = None
         self.drawing_sam_bbox = False
         self.start_point = None
         self.end_point = None
         self.current_mask = None
         self.point_prompts = []
         self.is_adding_points = False
         self.ui.accept_sam_btn.setEnabled(False)
         self.ui.reject_sam_btn.setEnabled(False)
         self.ui.reset_points_btn.setEnabled(False)
         # Keep the current tool active, but reset its drawing state
         if self.current_tool in ["point_prompt", "negative_point_prompt"]:
              self.is_adding_points = True # Re-enter point adding mode for the new image


    def load_annotations(self):
        """Load annotations for the current image (using canonical path) into the UI list."""
        self.ui.annotations_list.clear()

        if not self.current_image_path:
            logger.debug("load_annotations: No current image path set.")
            return

        # Directly access annotations using the canonical path
        image_annotations = self.annotations.get(self.current_image_path, [])

        logger.debug(f"Found {len(image_annotations)} annotations for canonical path: {self.current_image_path}")

        # Check if masks need to be recreated (e.g., loaded from state without image context)
        if self.current_image is not None:
             for i, annotation in enumerate(image_annotations):
                  if 'mask' not in annotation and annotation.get('type') in ['polygon', 'rectangle'] and 'points' in annotation:
                       logger.info(f"Recreating mask for annotation {i} of type {annotation.get('type')}")
                       mask = self.create_mask_from_annotation(annotation)
                       if mask is not None:
                            self.annotations[self.current_image_path][i]['mask'] = mask
                       else:
                            logger.warning(f"Failed to recreate mask for annotation {i}")


        # Populate the list widget
        for i, annotation in enumerate(image_annotations):
            class_id = annotation.get('class_id', 1) # Default to class 1 if missing
            class_name = self.class_names.get(class_id, f"Unknown Class {class_id}")
            # Use index + 1 for 1-based display
            item_text = f"Annot {i+1} - {class_name}"
            list_item = QListWidgetItem(item_text)
            # Optionally store annotation index or class_id in item data
            list_item.setData(Qt.ItemDataRole.UserRole, i)
            self.ui.annotations_list.addItem(list_item)

        # Update the visual display on the image
        self.update_annotation_display()


    def clear_all_annotations(self):
        """Clear all annotations for the current image."""
        if not self.current_image_path or self.current_image_path not in self.annotations:
            QMessageBox.information(self.ui, "Clear All Annotations", "No annotations to clear for the current image.")
            return

        # Count how many annotations will be deleted
        count = len(self.annotations[self.current_image_path])
        if count == 0:
            QMessageBox.information(self.ui, "Clear All Annotations", "No annotations to clear for the current image.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self.ui, "Confirm Clear All",
            f"Are you sure you want to remove all {count} annotations for this image?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply != QMessageBox.StandardButton.Yes:
            return

        # Get the directory to delete
        adv_seg_state_dir = self._get_adv_seg_state_dir()
        if adv_seg_state_dir:
            dir_name = self._get_image_annotation_dir_name(self.current_image_path)
            npz_dir = Path(adv_seg_state_dir) / dir_name

            # Delete the entire directory if it exists
            if npz_dir.is_dir():
                try:
                    import shutil
                    shutil.rmtree(npz_dir)
                    logger.info(f"Deleted annotation directory: {npz_dir}")
                except Exception as e:
                    logger.error(f"Error removing annotation directory {npz_dir}: {e}")

        # Clear annotations from memory
        self.annotations[self.current_image_path] = []

        # Exit editing mode if active
        if self.editing_mode:
            self.exit_editing_mode()

        # Update UI
        self.load_annotations()
        self.update_annotation_display()
        # No longer automatically saving state

        logger.info(f"Cleared all {count} annotations for image {self.current_image_path}")

    def on_annotation_selection_changed(self):
        """Handle changes in the annotation list selection."""
        # Update the display to highlight selected annotations
        self.update_annotation_display()

    def update_annotation_display(self, is_brush_update=False):
        """Update the annotation view with image and overlaid annotations."""
        if self.current_image is None:
            self.ui.annotation_image_view.set_pixmap(QPixmap())
            return

        # Start with a fresh copy of the original image
        display_image = self.current_image.copy() # Work on BGR copy

        # Get annotations for the current canonical path
        image_annotations = self.annotations.get(self.current_image_path, [])

        # Get selected annotation indices from the list widget
        selected_indices = []
        for item in self.ui.annotations_list.selectedItems():
            index = item.data(Qt.ItemDataRole.UserRole)
            if index is not None:
                selected_indices.append(index)
            else:
                # Fallback to row index if data not set
                selected_indices.append(self.ui.annotations_list.row(item))

        # --- Draw existing, saved annotations ---
        for i, annotation in enumerate(image_annotations):
            class_id = annotation.get('class_id', 1)
            qcolor = self.class_colors.get(class_id, QColor(255, 255, 0)) # Default yellow for unknown
            # Convert QColor(RGB) to BGR tuple for OpenCV
            color_bgr = (qcolor.blue(), qcolor.green(), qcolor.red())

            is_selected_for_editing = (self.current_tool == 'select' and self.editing_mode and self.selected_annotation_index == i)
            is_selected_in_list = i in selected_indices

            # Highlight annotations that are selected in the list or being edited
            line_thickness = 3 if (is_selected_for_editing or is_selected_in_list) else 2
            overlay_alpha = 0.5 if (is_selected_for_editing or is_selected_in_list) else 0.3

            # Use different colors for different selection states
            if is_selected_for_editing:
                contour_color = (0, 255, 255)  # Yellow for editing
            elif is_selected_in_list:
                contour_color = (255, 0, 255)  # Magenta for selected in list
            else:
                contour_color = color_bgr  # Same color as the fill for consistency

            mask = annotation.get('mask') # Get mask if it exists

            if mask is not None and mask.shape[:2] == display_image.shape[:2]:
                # Create colored overlay where mask is positive
                overlay = np.zeros_like(display_image)
                overlay[mask > 0] = color_bgr
                display_image = cv2.addWeighted(display_image, 1.0, overlay, overlay_alpha, 0)

                # Draw contour
                contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                cv2.drawContours(display_image, contours, -1, contour_color, line_thickness)

            # --- Draw editing handles if selected ---
            if is_selected_for_editing:
                points_to_draw = []
                if annotation.get('type') == 'polygon':
                    points_to_draw = self.editing_points # Use potentially modified points
                elif annotation.get('type') == 'rectangle':
                    # editing_points stores corners for rectangles during edit
                     if len(self.editing_points) == 4:
                          points_to_draw = self.editing_points

                # Draw editing points (handles)
                handle_radius = 6
                for j, point in enumerate(points_to_draw):
                    pt_int = (int(point[0]), int(point[1]))
                    handle_color = (0, 0, 255) # Default Red BGR
                    if j == self.hover_point_index: handle_color = (255, 0, 255) # Magenta BGR
                    if j == self.selected_point_index: handle_color = (255, 255, 0) # Cyan BGR

                    cv2.circle(display_image, pt_int, handle_radius, handle_color, -1) # Filled handle
                    cv2.circle(display_image, pt_int, handle_radius, (0, 0, 0), 1) # Black outline

        # --- Draw annotations currently being created ---
        # Polygon in progress
        if self.drawing and self.current_tool == 'polygon' and self.points:
            pts_np = np.array(self.points, dtype=np.int32)
            cv2.polylines(display_image, [pts_np], isClosed=False, color=(0, 255, 255), thickness=2) # Cyan
            for pt in self.points:
                cv2.circle(display_image, (int(pt[0]), int(pt[1])), 5, (0, 255, 255), -1) # Cyan filled points

        # Rectangle in progress (or SAM box)
        if self.drawing and self.current_tool == 'rectangle' and self.start_point and self.end_point:
            pt1 = (int(self.start_point[0]), int(self.start_point[1]))
            pt2 = (int(self.end_point[0]), int(self.end_point[1]))
            cv2.rectangle(display_image, pt1, pt2, (0, 255, 255), 2) # Cyan
        if self.drawing_sam_bbox and self.current_tool == 'magic_wand' and self.start_point and self.end_point:
             pt1 = (int(self.start_point[0]), int(self.start_point[1]))
             pt2 = (int(self.end_point[0]), int(self.end_point[1]))
             cv2.rectangle(display_image, pt1, pt2, (0, 255, 255), 2) # Cyan for SAM box too

        # Brush mask in progress
        if self.current_tool == 'brush' and self.brush_mask is not None and np.any(self.brush_mask):
            qcolor = self.class_colors.get(self.current_class, QColor(255, 0, 0)) # Use current class color
            color_bgr = (qcolor.blue(), qcolor.green(), qcolor.red())
            contour_color = color_bgr  # Same color as the fill for consistency
            overlay = np.zeros_like(display_image)
            overlay[self.brush_mask > 0] = color_bgr
            display_image = cv2.addWeighted(display_image, 1.0, overlay, 0.4, 0) # Semi-transparent
            contours, _ = cv2.findContours(self.brush_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(display_image, contours, -1, contour_color, 2)

        # Current SAM mask (before accept/reject)
        if self.current_mask is not None:
            sam_color = (0, 255, 255)  # Yellow BGR for pending mask
            mask_overlay = np.zeros_like(display_image)
            mask_overlay[self.current_mask > 0] = sam_color
            display_image = cv2.addWeighted(display_image, 1.0, mask_overlay, 0.5, 0)
            contours, _ = cv2.findContours(self.current_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(display_image, contours, -1, sam_color, 2) # Same yellow color for contour

        # SAM Point prompts
        if self.point_prompts:
            for i, (x, y, label) in enumerate(self.point_prompts):
                pt_int = (int(x), int(y))
                radius = 8
                border_color = (255, 255, 255) # White
                thickness = 2
                if label == 1: # Positive
                    fill_color = (0, 255, 0) # Green BGR
                    cv2.circle(display_image, pt_int, radius + thickness, border_color, thickness)
                    cv2.circle(display_image, pt_int, radius, fill_color, -1)
                    # Plus sign
                    cv2.line(display_image, (pt_int[0]-4, pt_int[1]), (pt_int[0]+4, pt_int[1]), border_color, thickness)
                    cv2.line(display_image, (pt_int[0], pt_int[1]-4), (pt_int[0], pt_int[1]+4), border_color, thickness)
                else: # Negative
                    fill_color = (0, 0, 255) # Red BGR
                    cv2.circle(display_image, pt_int, radius + thickness, border_color, thickness)
                    cv2.circle(display_image, pt_int, radius, fill_color, -1)
                    # Minus sign
                    cv2.line(display_image, (pt_int[0]-4, pt_int[1]), (pt_int[0]+4, pt_int[1]), border_color, thickness)

                # Optional: Point index label
                # cv2.putText(display_image, str(i+1), (pt_int[0]+15, pt_int[1]+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
                # cv2.putText(display_image, str(i+1), (pt_int[0]+15, pt_int[1]+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, border_color, 1)


        # Brush/Erase cursor preview
        if self.current_tool in ['brush', 'erase'] and self.last_brush_point:
            pt_int = (int(self.last_brush_point[0]), int(self.last_brush_point[1]))
            cursor_color = (0, 255, 255) if self.current_tool == 'brush' else (255, 0, 0) # Yellow or Blue BGR
            cv2.circle(display_image, pt_int, self.brush_size, cursor_color, 1) # Outline only


        # --- Final Display Update ---
        # Convert the final BGR image to QPixmap for display
        final_pixmap = convert_cvimage_to_qpixmap(display_image, already_rgb=False)
        if not final_pixmap.isNull():
             # Preserve zoom, use is_brush_update hint for smoother updates
             self.ui.annotation_image_view.set_pixmap(final_pixmap, preserve_zoom=True, is_brush_update=is_brush_update)
        else:
             logger.error("Failed to create final QPixmap for display.")


    def on_annotation_view_mouse_press(self, event: QMouseEvent):
        """Handle mouse press events on the annotation view."""
        if self.current_image is None: return

        # Map view coordinates to image coordinates
        pos_scene = self.ui.annotation_image_view.mapToScene(event.pos())
        img_x, img_y = int(pos_scene.x()), int(pos_scene.y())

        # Ensure coordinates are within image bounds (important!)
        h, w = self.current_image.shape[:2]
        if not (0 <= img_x < w and 0 <= img_y < h):
             logger.debug(f"Mouse press ({img_x}, {img_y}) outside image bounds (0,0,{w},{h})")
             return # Click outside image

        tool = self.current_tool

        # --- Select Tool ---
        if tool == 'select':
            if event.button() == Qt.MouseButton.LeftButton:
                # Check for control point click first if in editing mode
                if self.editing_mode and self.selected_annotation_index is not None:
                     point_idx = self.find_control_point(img_x, img_y, self.selected_annotation_index)
                     if point_idx is not None:
                          self.selected_point_index = point_idx
                          self.drag_point = True
                          logger.info(f"Started dragging control point {point_idx}")
                          self.update_annotation_display() # Highlight selected point
                          return # Don't try to select annotation

                # If not dragging a point, try selecting an annotation
                annot_idx = self.find_annotation_at_point(img_x, img_y)
                if annot_idx is not None:
                     if annot_idx == self.selected_annotation_index and self.editing_mode:
                          pass # Clicked again on already selected annotation, do nothing special
                     else:
                          self.select_annotation(annot_idx) # Select or re-select
                          logger.info(f"Selected annotation {annot_idx}")
                else:
                     # Clicked on empty space, deselect
                     self.exit_editing_mode()
                     logger.info("Deselected annotation (clicked empty space)")

            elif event.button() == Qt.MouseButton.RightButton:
                # Show context menu if clicking on a selected or existing annotation
                annot_idx = self.selected_annotation_index
                if annot_idx is None: # If nothing selected, check if clicked on one
                     annot_idx = self.find_annotation_at_point(img_x, img_y)
                     if annot_idx is not None: self.select_annotation(annot_idx) # Select it first

                if annot_idx is not None:
                     self.show_annotation_context_menu(event.globalPos())

        # --- Polygon Tool ---
        elif tool == 'polygon':
            # Double click finishes polygon
            if event.type() == QEvent.Type.MouseButtonDblClick and event.button() == Qt.MouseButton.LeftButton:
                 if len(self.points) >= 3:
                      self.complete_polygon()
                 else: # Not enough points, treat as single click
                      if not self.drawing:
                           self.drawing = True
                           self.points = []
                      self.points.append((img_x, img_y))
                      self.update_annotation_display()

            # Right click finishes polygon
            elif event.button() == Qt.MouseButton.RightButton:
                 if len(self.points) >= 3:
                      self.complete_polygon()
                 else: # Not enough points, cancel drawing maybe?
                      self.drawing = False
                      self.points = []
                      self.update_annotation_display()
                      logger.info("Cancelled polygon drawing (right-click with < 3 points)")

            # Left click adds point
            elif event.button() == Qt.MouseButton.LeftButton:
                 if not self.drawing:
                      self.drawing = True
                      self.points = [] # Start new polygon
                 self.points.append((img_x, img_y))
                 self.update_annotation_display()

        # --- Rectangle Tool ---
        elif tool == 'rectangle':
             if event.button() == Qt.MouseButton.LeftButton:
                  self.drawing = True
                  self.start_point = (img_x, img_y)
                  self.end_point = (img_x, img_y)
                  self.update_annotation_display()

        # --- Point Prompt Tools ---
        elif tool in ['point_prompt', 'negative_point_prompt']:
             if event.button() == Qt.MouseButton.LeftButton:
                  label = 1 if tool == 'point_prompt' else 0
                  point_radius_sq = 10**2 # Selection radius squared

                  # Check if clicking near an existing point to remove it
                  removed = False
                  for i, (px, py, _) in enumerate(self.point_prompts):
                       if (img_x - px)**2 + (img_y - py)**2 <= point_radius_sq:
                            del self.point_prompts[i]
                            logger.info(f"Removed point prompt at ({px}, {py})")
                            removed = True
                            break # Only remove one point per click

                  if not removed:
                       # Add new point
                       self.point_prompts.append((img_x, img_y, label))
                       logger.info(f"Added {'positive' if label==1 else 'negative'} point prompt at ({img_x}, {img_y})")

                  # Update SAM prediction and UI
                  self.predict_mask_from_points() # Will handle display update and button states

        # --- Brush/Erase Tools ---
        elif tool in ['brush', 'erase']:
            if event.button() == Qt.MouseButton.LeftButton:
                 self.drawing = True # Indicate mouse button is down
                 self.last_brush_point = (img_x, img_y)
                 self.apply_brush_or_erase(img_x, img_y) # Apply first dab
                 self.update_annotation_display(is_brush_update=True)

        # --- Magic Wand (Box) Tool ---
        elif tool == 'magic_wand':
            if event.button() == Qt.MouseButton.LeftButton:
                 self.drawing_sam_bbox = True
                 self.start_point = (img_x, img_y)
                 self.end_point = (img_x, img_y)
                 self.update_annotation_display()


    def show_annotation_context_menu(self, global_pos):
         """Displays the right-click context menu for the selected annotation."""
         if self.selected_annotation_index is None: return

         context_menu = QMenu(self.ui)

         # --- Change Class Submenu ---
         class_menu = QMenu("Change Class", context_menu)
         sorted_class_ids = sorted(self.class_names.keys())

         # Use partial to correctly capture class_id in the loop
         from functools import partial
         for class_id in sorted_class_ids:
              class_name = self.class_names.get(class_id, f"Unknown {class_id}")
              action = class_menu.addAction(class_name)
              # Use partial to pass the correct class_id at the time of creation
              action.triggered.connect(partial(self.change_selected_annotation_class, class_id))
         context_menu.addMenu(class_menu)

         context_menu.addSeparator()

         # --- Edit Shape Action ---
         edit_action = context_menu.addAction("Edit Shape")
         edit_action.triggered.connect(self.enter_editing_mode_for_selected)

         # --- Delete Action ---
         delete_action = context_menu.addAction("Delete Annotation")
         delete_action.triggered.connect(self.delete_selected_annotation)

         context_menu.exec(global_pos)


    def on_annotation_view_mouse_move(self, event: QMouseEvent):
        """Handle mouse move events on the annotation view."""
        if self.current_image is None: return

        pos_scene = self.ui.annotation_image_view.mapToScene(event.pos())
        img_x, img_y = int(pos_scene.x()), int(pos_scene.y())
        h, w = self.current_image.shape[:2]

        # Clamp coordinates to image bounds if needed, or just track last valid point?
        # Clamping might feel weird if cursor goes outside. Let's track last point.
        last_valid_pos = (max(0, min(img_x, w-1)), max(0, min(img_y, h-1)))

        tool = self.current_tool

        # --- Select Tool ---
        if tool == 'select':
            if self.drag_point and self.selected_point_index is not None and self.editing_mode:
                # Update dragged control point position
                if 0 <= self.selected_point_index < len(self.editing_points):
                     # Update using the potentially clamped coordinates
                     self.editing_points[self.selected_point_index] = last_valid_pos
                     self.update_annotation_display()
            else:
                 # Hover effect for control points or annotations
                 new_hover_point_index = None
                 cursor = Qt.CursorShape.ArrowCursor
                 if self.editing_mode and self.selected_annotation_index is not None:
                      new_hover_point_index = self.find_control_point(img_x, img_y, self.selected_annotation_index)
                      if new_hover_point_index is not None:
                           cursor = Qt.CursorShape.PointingHandCursor
                 elif self.find_annotation_at_point(img_x, img_y) is not None:
                      cursor = Qt.CursorShape.PointingHandCursor

                 # Update hover state and cursor only if changed
                 if new_hover_point_index != self.hover_point_index:
                      self.hover_point_index = new_hover_point_index
                      self.update_annotation_display() # Redraw for hover highlight
                 if self.ui.annotation_image_view.cursor().shape() != cursor:
                      self.ui.annotation_image_view.setCursor(cursor)


        # --- Polygon Tool ---
        # (No action on move unless drawing line to cursor, could be added)

        # --- Rectangle Tool ---
        elif tool == 'rectangle' and self.drawing:
            self.end_point = last_valid_pos # Update endpoint with clamped coords
            self.update_annotation_display()

        # --- Point Prompt Tools ---
        # (No action on move)

        # --- Brush/Erase Tools ---
        elif tool in ['brush', 'erase']:
             # Update cursor preview position
             self.last_brush_point = last_valid_pos
             needs_update = True # Assume update needed for cursor

             # If left button is down (drawing)
             if event.buttons() & Qt.MouseButton.LeftButton and self.drawing:
                  self.apply_brush_or_erase(last_valid_pos[0], last_valid_pos[1]) # Apply dab at current pos
                  # Interpolate if previous point exists
                  if hasattr(self, '_prev_brush_pos') and self._prev_brush_pos:
                      self.interpolate_brush_stroke(self._prev_brush_pos, last_valid_pos)
                  self._prev_brush_pos = last_valid_pos # Store current pos for next move
                  needs_update = True # Definitely need update

             if needs_update:
                 self.update_annotation_display(is_brush_update=True) # Update display (includes cursor)

        # --- Magic Wand (Box) Tool ---
        elif tool == 'magic_wand' and self.drawing_sam_bbox:
            self.end_point = last_valid_pos
            self.update_annotation_display()


    def on_annotation_view_mouse_release(self, event: QMouseEvent):
        """Handle mouse release events on the annotation view."""
        if self.current_image is None: return

        pos_scene = self.ui.annotation_image_view.mapToScene(event.pos())
        img_x, img_y = int(pos_scene.x()), int(pos_scene.y())
        # We might not need the exact release coordinates for most tools

        tool = self.current_tool

        # --- Select Tool ---
        if tool == 'select':
            if self.drag_point: # Finished dragging a control point
                 self.drag_point = False
                 # Update the actual annotation based on self.editing_points
                 self.update_annotation_from_editing_points()
                 self.selected_point_index = None # Deselect point
                 self.update_annotation_display()
                 logger.info(f"Finished editing annotation {self.selected_annotation_index}")

        # --- Polygon Tool ---
        # (Completion handled on double-click or right-click in press event)

        # --- Rectangle Tool ---
        elif tool == 'rectangle' and self.drawing:
            if self.start_point and self.end_point and event.button() == Qt.MouseButton.LeftButton:
                 # Ensure start is top-left, end is bottom-right (optional, but good practice)
                 x1, y1 = self.start_point
                 x2, y2 = self.end_point
                 start_final = (min(x1, x2), min(y1, y2))
                 end_final = (max(x1, x2), max(y1, y2))

                 # Add annotation if size is reasonable (prevent tiny rectangles on click)
                 if abs(x1-x2) > 3 and abs(y1-y2) > 3:
                      annotation = {
                           'type': 'rectangle',
                           'points': [start_final, end_final],
                           'class_id': self.current_class
                      }
                      self.add_annotation(annotation)
                      logger.info(f"Added rectangle annotation from {start_final} to {end_final}")
                      # No longer automatically saving state
                 else:
                      logger.info("Rectangle too small, not added.")

            # Reset drawing state regardless
            self.drawing = False
            self.start_point = None
            self.end_point = None
            self.update_annotation_display() # Clear the drawing rectangle

        # --- Point Prompt Tools ---
        # (No action on release)

        # --- Brush/Erase Tools ---
        elif tool in ['brush', 'erase']:
            if event.button() == Qt.MouseButton.LeftButton:
                self.drawing = False # Stop drawing flag
                self._prev_brush_pos = None # Clear previous position for interpolation

                if tool == 'brush' and self.brush_mask is not None and np.any(self.brush_mask):
                     # Promote brush mask to current_mask for accept/reject
                     self.current_mask = self.brush_mask.copy()
                     self.brush_mask = None # Clear the temporary drawing mask
                     self.ui.accept_sam_btn.setEnabled(True)
                     self.ui.reject_sam_btn.setEnabled(True)
                     logger.info("Brush stroke finished. Use Accept/Reject.")
                     self.update_annotation_display() # Show the final mask clearly
                elif tool == 'erase' and self._annotations_modified:
                    # Save state if erasing modified annotations
                    self._save_state_after_erase()

                # Always clear the temporary brush point display on release
                self.last_brush_point = None
                self.update_annotation_display()


        # --- Magic Wand (Box) Tool ---
        elif tool == 'magic_wand' and self.drawing_sam_bbox:
            if self.start_point and self.end_point and event.button() == Qt.MouseButton.LeftButton:
                 # Finalize box coordinates
                 x1, y1 = self.start_point
                 x2, y2 = self.end_point
                 x_min, y_min = min(x1, x2), min(y1, y2)
                 x_max, y_max = max(x1, x2), max(y1, y2)
                 bbox = [x_min, y_min, x_max, y_max]

                 # Predict from box if size is reasonable
                 if x_max > x_min + 3 and y_max > y_min + 3:
                      logger.info(f"Predicting SAM mask from box: {bbox}")
                      self.predict_mask_from_box(bbox)
                 else:
                      logger.info("Magic Wand box too small, prediction cancelled.")

            # Reset drawing state
            self.drawing_sam_bbox = False
            self.start_point = None
            self.end_point = None
            self.update_annotation_display() # Clear the drawing box

    def complete_polygon(self):
         """Finalizes the polygon being drawn."""
         if not self.drawing or self.current_tool != 'polygon' or len(self.points) < 3:
              return # Should not happen if called correctly

         self.drawing = False
         annotation = {
              'type': 'polygon',
              'points': self.points.copy(),
              'class_id': self.current_class
         }
         self.add_annotation(annotation)
         logger.info(f"Added polygon annotation with {len(self.points)} points.")
         # No longer automatically saving state

         self.points = [] # Reset for next polygon
         self.update_annotation_display()


    def apply_brush_or_erase(self, x, y):
         """Applies the brush or erase effect at the given image coordinates."""
         if self.current_image is None: return
         h, w = self.current_image.shape[:2]

         # Define the circular brush area efficiently
         radius = self.brush_size
         y_min, y_max = max(0, y - radius), min(h, y + radius + 1)
         x_min, x_max = max(0, x - radius), min(w, x + radius + 1)

         # Create coordinate grids *only* for the bounding box of the brush
         yy, xx = np.mgrid[y_min:y_max, x_min:x_max]
         # Calculate distance squared for efficiency
         dist_sq = (xx - x)**2 + (yy - y)**2
         brush_area_mask = dist_sq <= radius**2 # Boolean mask within the bbox

         # --- Brush Tool ---
         if self.current_tool == 'brush':
              # Initialize brush mask if it doesn't exist for this stroke
              if self.brush_mask is None:
                   self.brush_mask = np.zeros((h, w), dtype=np.uint8)
              # Apply brush (set to 255) within the circular area
              self.brush_mask[y_min:y_max, x_min:x_max][brush_area_mask] = 255

         # --- Erase Tool ---
         elif self.current_tool == 'erase':
              modified_any = False
              annotations_to_remove = []
              current_annotations = self.annotations.get(self.current_image_path, [])

              for i, annotation in enumerate(current_annotations):
                   # Only erase from mask-based annotations
                   if 'mask' in annotation and isinstance(annotation['mask'], np.ndarray):
                        mask = annotation['mask']
                        # Check if the brush bbox overlaps the mask bbox (quick pre-check)
                        # (Could add this optimization if needed, for now check pixels)

                        # Get the section of the annotation mask corresponding to the brush bbox
                        mask_section = mask[y_min:y_max, x_min:x_max]

                        # Find where the brush circle (within bbox) overlaps the annotation mask section
                        erase_pixels = np.logical_and(brush_area_mask, mask_section > 0)

                        if np.any(erase_pixels):
                             # Erase by setting mask pixels to 0 in the affected area
                             mask[y_min:y_max, x_min:x_max][erase_pixels] = 0
                             modified_any = True

                             # Check if the entire annotation mask became empty
                             if not np.any(mask):
                                  annotations_to_remove.append(i)
                                  logger.info(f"Annotation {i} marked for removal (fully erased).")

              # Remove annotations that were fully erased (iterate in reverse)
              if annotations_to_remove:
                   for i in sorted(annotations_to_remove, reverse=True):
                        logger.info(f"Removing fully erased annotation at index {i}")
                        del self.annotations[self.current_image_path][i]
                   # Need to update the list widget after modification
                   self.load_annotations() # Reloads list from modified self.annotations

              if modified_any:
                   self._annotations_modified = True # Flag that state needs saving

    def interpolate_brush_stroke(self, pt1, pt2):
         """Interpolates between two points to create a continuous brush stroke."""
         x1, y1 = pt1
         x2, y2 = pt2
         dx, dy = x2 - x1, y2 - y1
         distance = np.sqrt(dx**2 + dy**2)

         # Determine number of steps based on brush size to avoid gaps
         # Add 1 to step count to include the endpoint implicitly if needed
         steps = max(1, int(distance / (self.brush_size * 0.5)) + 1)

         if steps <= 1: return # Points are close enough

         for i in range(1, steps): # Exclude start point (already applied), include intermediate
              t = i / steps
              inter_x = int(x1 + t * dx)
              inter_y = int(y1 + t * dy)
              self.apply_brush_or_erase(inter_x, inter_y)


    def predict_mask_from_points(self):
        """Predict mask from the current point prompts using SAM."""
        if not self.point_prompts or not self.sam_handler.predictor:
            self.current_mask = None # Ensure no stale mask if no points/predictor
            self.ui.accept_sam_btn.setEnabled(False)
            self.ui.reject_sam_btn.setEnabled(False)
            # Reset button enabled state handled below based on prompts list
        else:
            point_coords = np.array([[x, y] for x, y, _ in self.point_prompts])
            point_labels = np.array([l for _, _, l in self.point_prompts])

            logger.info(f"Predicting SAM mask from {len(self.point_prompts)} points.")
            masks, scores, _ = self.sam_handler.predict_from_points(point_coords, point_labels)

            if masks is not None and len(masks) > 0:
                best_mask_idx = np.argmax(scores)
                self.current_mask = masks[best_mask_idx]
                self.ui.accept_sam_btn.setEnabled(True)
                self.ui.reject_sam_btn.setEnabled(True)
                logger.info(f"SAM prediction successful, best score: {scores[best_mask_idx]:.4f}")
            else:
                self.current_mask = None
                self.ui.accept_sam_btn.setEnabled(False)
                self.ui.reject_sam_btn.setEnabled(False)
                logger.warning("SAM prediction from points returned no masks.")

        # Update reset button state based on whether points exist
        self.ui.reset_points_btn.setEnabled(len(self.point_prompts) > 0)
        self.update_annotation_display() # Show points and potentially the mask


    def predict_mask_from_box(self, bbox):
         """Predict mask from a bounding box using SAM."""
         if not self.sam_handler.predictor:
              logger.warning("Cannot predict from box: SAM predictor not available.")
              return

         logger.info(f"Predicting SAM mask from box: {bbox}")
         masks, scores, _ = self.sam_handler.predict_from_box(bbox)

         if masks is not None and len(masks) > 0:
              best_mask_idx = np.argmax(scores)
              self.current_mask = masks[best_mask_idx]
              self.ui.accept_sam_btn.setEnabled(True)
              self.ui.reject_sam_btn.setEnabled(True)
              logger.info(f"SAM box prediction successful, best score: {scores[best_mask_idx]:.4f}")
         else:
              self.current_mask = None
              self.ui.accept_sam_btn.setEnabled(False)
              self.ui.reject_sam_btn.setEnabled(False)
              logger.warning("SAM prediction from box returned no masks.")

         self.update_annotation_display() # Show the resulting mask or clear previous


    def reset_points(self):
        """Clear SAM point prompts and the current prediction."""
        logger.info("Resetting SAM point prompts.")
        self.point_prompts = []
        self.current_mask = None
        # Keep the tool active, but clear state
        self.is_adding_points = self.current_tool in ['point_prompt', 'negative_point_prompt']
        self.ui.accept_sam_btn.setEnabled(False)
        self.ui.reject_sam_btn.setEnabled(False)
        self.ui.reset_points_btn.setEnabled(False)
        self.update_annotation_display()

    def accept_mask(self):
        """Accept the current SAM/Brush mask and add it as an annotation."""
        if self.current_mask is None:
            logger.warning("Accept mask called but no current mask exists.")
            return

        logger.info("Accepting current mask.")
        annotation = {
            'type': 'mask', # Generic mask type
            'mask': self.current_mask.copy(), # Store a copy
            'class_id': self.current_class
        }
        self.add_annotation(annotation)

        # Reset state after accepting
        self.current_mask = None
        self.point_prompts = [] # Clear points if mask came from points
        self.brush_mask = None # Clear brush mask if mask came from brush
        self.is_adding_points = False # Exit point adding mode

        self.ui.accept_sam_btn.setEnabled(False)
        self.ui.reject_sam_btn.setEnabled(False)
        self.ui.reset_points_btn.setEnabled(len(self.point_prompts) > 0) # Likely false now

        self.update_annotation_display()
        # No longer automatically saving state

    def reject_mask(self):
        """Reject the current SAM/Brush mask."""
        if self.current_mask is None and self.brush_mask is None:
             logger.warning("Reject mask called but no current mask or brush mask exists.")
             return

        logger.info("Rejecting current mask.")
        # Reset state without adding annotation
        self.current_mask = None
        # Don't clear points automatically on reject, user might want to adjust them
        # self.point_prompts = []
        self.brush_mask = None # Clear brush mask if rejecting that
        self.is_adding_points = self.current_tool in ['point_prompt', 'negative_point_prompt'] # Stay in mode if active

        self.ui.accept_sam_btn.setEnabled(False)
        self.ui.reject_sam_btn.setEnabled(False)
        # Keep reset button enabled if points still exist
        self.ui.reset_points_btn.setEnabled(len(self.point_prompts) > 0)

        self.update_annotation_display()


    def _regenerate_masks_from_points(self, image_path):
        """Regenerate masks for annotations that have points but no mask."""
        if image_path not in self.annotations:
            logger.warning(f"Cannot regenerate masks: No annotations for {image_path}")
            return

        # Get the image dimensions
        image_dimensions = None
        if self.current_image_path == image_path and self.current_image is not None:
            # If this is the current image, we already have it loaded
            image_dimensions = self.current_image.shape[:2]
        else:
            # Otherwise, we need to load the image to get its dimensions
            try:
                abs_path = self._get_absolute_path(image_path)
                if abs_path and os.path.exists(abs_path):
                    img = cv2.imread(abs_path)
                    if img is not None:
                        image_dimensions = img.shape[:2]
            except Exception as e:
                logger.error(f"Error loading image to regenerate masks: {e}")

        if image_dimensions is None:
            logger.warning(f"Cannot regenerate masks: Could not determine image dimensions for {image_path}")
            return

        h, w = image_dimensions
        regenerated_count = 0

        for i, annotation in enumerate(self.annotations[image_path]):
            if 'mask' not in annotation and annotation.get('type') in ['polygon', 'rectangle'] and 'points' in annotation:
                logger.info(f"Regenerating mask for annotation {i} of type {annotation.get('type')} for image {image_path}")

                points = annotation.get('points')
                annot_type = annotation.get('type')
                mask = np.zeros((h, w), dtype=np.uint8)

                try:
                    if annot_type == 'polygon' and points and len(points) >= 3:
                        pts_np = np.array(points, dtype=np.int32)
                        cv2.fillPoly(mask, [pts_np], 255)
                        self.annotations[image_path][i]['mask'] = mask
                        regenerated_count += 1
                    elif annot_type == 'rectangle' and points and len(points) == 2:
                        pt1, pt2 = points
                        x1, y1 = int(min(pt1[0], pt2[0])), int(min(pt1[1], pt2[1]))
                        x2, y2 = int(max(pt1[0], pt2[0])), int(max(pt1[1], pt2[1]))
                        # Ensure coordinates are within bounds
                        x1, y1 = max(0, x1), max(0, y1)
                        x2, y2 = min(w - 1, x2), min(h - 1, y2)
                        if x1 < x2 and y1 < y2: # Check if rectangle has valid dimensions
                            cv2.rectangle(mask, (x1, y1), (x2, y2), 255, -1) # Filled
                            self.annotations[image_path][i]['mask'] = mask
                            regenerated_count += 1
                except Exception as e:
                    logger.error(f"Error regenerating mask for annotation {i}: {e}")

        if regenerated_count > 0:
            logger.info(f"Successfully regenerated {regenerated_count} masks for image {image_path}")
            # No longer automatically saving state
        else:
            logger.info(f"No masks needed regeneration for image {image_path}")

    def create_mask_from_annotation(self, annotation):
        """Create a binary mask from polygon or rectangle 'points' data."""
        if self.current_image is None:
            logger.error("Cannot create mask: current_image is None.")
            return None

        h, w = self.current_image.shape[:2]
        mask = np.zeros((h, w), dtype=np.uint8)
        points = annotation.get('points')
        annot_type = annotation.get('type')

        try:
            if annot_type == 'polygon' and points and len(points) >= 3:
                pts_np = np.array(points, dtype=np.int32)
                cv2.fillPoly(mask, [pts_np], 255)
                return mask
            elif annot_type == 'rectangle' and points and len(points) == 2:
                pt1, pt2 = points
                x1, y1 = int(min(pt1[0], pt2[0])), int(min(pt1[1], pt2[1]))
                x2, y2 = int(max(pt1[0], pt2[0])), int(max(pt1[1], pt2[1]))
                # Ensure coordinates are within bounds
                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(w - 1, x2), min(h - 1, y2)
                if x1 < x2 and y1 < y2: # Check if rectangle has valid dimensions
                    cv2.rectangle(mask, (x1, y1), (x2, y2), 255, -1) # Filled
                return mask
            else:
                logger.warning(f"Cannot create mask for annotation type '{annot_type}' or invalid points: {points}")
                return None # Return None if mask creation fails
        except Exception as e:
            logger.error(f"Error creating mask from annotation points: {e}")
            return None


    def add_annotation(self, annotation):
        """Add a finalized annotation object to the current image's list."""
        if not self.current_image_path:
            logger.error("Cannot add annotation: No current image selected.")
            return
        if not isinstance(annotation, dict):
             logger.error(f"Cannot add annotation: Invalid format - expected dict, got {type(annotation)}.")
             return

        # Ensure 'mask' exists for polygon/rectangle if possible
        if annotation.get('type') in ['polygon', 'rectangle'] and 'mask' not in annotation:
            if self.current_image is not None:
                 mask = self.create_mask_from_annotation(annotation)
                 if mask is not None:
                      annotation['mask'] = mask
                 else:
                      logger.warning("Could not create mask when adding polygon/rectangle annotation.")
            else:
                 logger.warning("Cannot create mask for polygon/rectangle annotation yet: current_image not loaded.")


        # Add to the dictionary using the canonical path
        if self.current_image_path not in self.annotations:
            self.annotations[self.current_image_path] = []

        self.annotations[self.current_image_path].append(annotation)
        
        # Mark annotations as modified
        self._annotations_modified = True

        # Update the UI list
        self.load_annotations() # Reloads the list widget
        logger.debug(f"Added {annotation.get('type', 'unknown')} annotation. Total for image: {len(self.annotations[self.current_image_path])}")


    def find_annotation_at_point(self, x, y):
        """Find index of the topmost annotation covering the given image coordinates."""
        if not self.current_image_path or self.current_image_path not in self.annotations:
            return None
        if self.current_image is None: return None # Cannot check bounds

        h, w = self.current_image.shape[:2]
        if not (0 <= x < w and 0 <= y < h): return None # Out of bounds

        # Iterate in reverse to find the topmost annotation first
        current_annotations = self.annotations[self.current_image_path]
        for i in range(len(current_annotations) - 1, -1, -1):
            annotation = current_annotations[i]
            # Prioritize checking the mask if it exists
            if 'mask' in annotation and isinstance(annotation['mask'], np.ndarray):
                 mask = annotation['mask']
                 # Check mask bounds just in case
                 if 0 <= y < mask.shape[0] and 0 <= x < mask.shape[1] and mask[y, x] > 0:
                      return i
            # Fallback to geometric checks if no mask or point not in mask
            elif annotation.get('type') == 'polygon' and 'points' in annotation and len(annotation['points']) >=3 :
                 pts_np = np.array(annotation['points'], dtype=np.float32) # pointPolygonTest needs float
                 if cv2.pointPolygonTest(pts_np, (float(x), float(y)), False) >= 0:
                      return i
            elif annotation.get('type') == 'rectangle' and 'points' in annotation and len(annotation['points']) == 2:
                 pt1, pt2 = annotation['points']
                 x1, y1 = min(pt1[0], pt2[0]), min(pt1[1], pt2[1])
                 x2, y2 = max(pt1[0], pt2[0]), max(pt1[1], pt2[1])
                 if x1 <= x <= x2 and y1 <= y <= y2:
                      return i
        return None # No annotation found at this point

    def find_control_point(self, x, y, annotation_index):
        """Find the index of the control point near (x,y) for the given annotation."""
        if not self.editing_mode or annotation_index != self.selected_annotation_index:
            return None # Can only find points for the currently selected annotation in edit mode

        # Use editing_points which holds the current handle positions
        points_to_check = self.editing_points
        radius_sq = 10**2 # Detection radius squared

        for i, point in enumerate(points_to_check):
            px, py = point
            if (x - px)**2 + (y - py)**2 <= radius_sq:
                return i
        return None

    def select_annotation(self, annotation_index):
        """Select an annotation, potentially entering editing mode."""
        if not self.current_image_path or self.current_image_path not in self.annotations:
             logger.warning("Cannot select annotation: No annotations for current image.")
             return

        current_annotations = self.annotations[self.current_image_path]
        if not (0 <= annotation_index < len(current_annotations)):
             logger.warning(f"Cannot select annotation: Invalid index {annotation_index}")
             # Deselect if index is invalid
             self.exit_editing_mode()
             return

        # If clicking the same annotation that's already being edited, do nothing
        if self.editing_mode and self.selected_annotation_index == annotation_index:
             return

        # Exit previous editing mode if selecting a different annotation
        if self.editing_mode:
             self.exit_editing_mode()

        # Set selection
        self.selected_annotation_index = annotation_index
        annotation = current_annotations[annotation_index]

        # --- Prepare for Editing (if applicable) ---
        # For now, selecting always enters editing mode immediately if Select tool is active
        if self.current_tool == 'select':
             self.enter_editing_mode_for_selected()
        # --- End Prepare for Editing ---

        # Highlight in list widget
        if 0 <= annotation_index < self.ui.annotations_list.count():
            self.ui.annotations_list.setCurrentRow(annotation_index)
        else:
             logger.warning(f"Annotation index {annotation_index} out of sync with list widget count {self.ui.annotations_list.count()}")


        self.update_annotation_display() # Show selection highlight


    def enter_editing_mode_for_selected(self):
        """Sets up the state for editing the currently selected annotation."""
        if self.selected_annotation_index is None:
             logger.warning("Tried to enter editing mode, but no annotation selected.")
             return
        if not self.current_image_path or self.current_image_path not in self.annotations:
             return # Should not happen if index is valid

        annotation = self.annotations[self.current_image_path][self.selected_annotation_index]
        annot_type = annotation.get('type')

        # Prepare editing points based on type
        if annot_type == 'polygon' and 'points' in annotation:
             self.editing_points = [tuple(p) for p in annotation['points']] # Use copy
        elif annot_type == 'rectangle' and 'points' in annotation and len(annotation['points']) == 2:
             # Convert rectangle to 4 corner points for editing
             pt1, pt2 = annotation['points']
             x1, y1 = min(pt1[0], pt2[0]), min(pt1[1], pt2[1])
             x2, y2 = max(pt1[0], pt2[0]), max(pt1[1], pt2[1])
             self.editing_points = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
        elif annot_type == 'mask':
            logger.info("Editing mode for mask type is not implemented (no points).")
            # Cannot edit masks directly with points yet
            # self.selected_annotation_index = None # Deselect if cannot edit
            # self.update_annotation_display()
            # Maybe allow changing class? For now, just log.
            self.editing_points = []
            # Don't set editing_mode = True if we can't edit
            return # Exit without entering edit mode
        else:
             logger.warning(f"Cannot enter editing mode for annotation type '{annot_type}' or missing points.")
             self.editing_points = []
             return # Exit without entering edit mode


        self.editing_mode = True
        self.selected_point_index = None
        self.hover_point_index = None
        self.drag_point = False
        self.update_annotation_display() # Show editing handles
        logger.info(f"Entered editing mode for annotation {self.selected_annotation_index}")


    def update_annotation_from_editing_points(self):
         """Updates the selected annotation object based on self.editing_points."""
         if not self.editing_mode or self.selected_annotation_index is None: return
         if not self.current_image_path or self.current_image_path not in self.annotations: return

         annotation = self.annotations[self.current_image_path][self.selected_annotation_index]
         annot_type = annotation.get('type')

         logger.info(f"Updating annotation {self.selected_annotation_index} from edited points.")

         if annot_type == 'polygon':
              if len(self.editing_points) >= 3:
                   annotation['points'] = [tuple(p) for p in self.editing_points] # Save copy
              else:
                   logger.warning("Cannot update polygon: Fewer than 3 editing points.")
                   return # Don't update if invalid

         elif annot_type == 'rectangle':
              if len(self.editing_points) == 4:
                   # Convert 4 corners back to top-left, bottom-right
                   x_coords = [p[0] for p in self.editing_points]
                   y_coords = [p[1] for p in self.editing_points]
                   x1, y1 = min(x_coords), min(y_coords)
                   x2, y2 = max(x_coords), max(y_coords)
                   annotation['points'] = [(x1, y1), (x2, y2)]
              else:
                   logger.warning("Cannot update rectangle: Incorrect number of editing points.")
                   return # Don't update if invalid
         else:
              logger.warning(f"Cannot update annotation of type '{annot_type}' from editing points.")
              return

         # --- Update the mask ---
         if self.current_image is not None:
             mask = self.create_mask_from_annotation(annotation)
             if mask is not None:
                 annotation['mask'] = mask
             else:
                 # Remove potentially outdated mask if creation failed
                 if 'mask' in annotation: del annotation['mask']
                 logger.warning("Failed to update mask after editing points.")
         else:
             # Remove mask if image context lost, rely on points for later recreation
             if 'mask' in annotation: del annotation['mask']
             logger.warning("Removed mask after editing points: No current image context.")

         self.save_state() # Save changes

    def edit_annotation(self):
        """Show dialog or enter direct editing mode for selected annotation in list."""
        selected_items = self.ui.annotations_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self.ui, "Edit Annotation", "Please select an annotation from the list.")
            return

        # If multiple items are selected, inform the user that only the first one will be edited
        if len(selected_items) > 1:
            QMessageBox.information(
                self.ui,
                "Multiple Selections",
                "Multiple annotations are selected. Only the first one will be edited."
            )

        # Get the first selected item
        list_item = selected_items[0]
        annotation_index = list_item.data(Qt.ItemDataRole.UserRole) # Get index from item data

        if annotation_index is None:
             # Fallback: use row index if data not set
             annotation_index = self.ui.annotations_list.row(list_item)

        # Validate index against current annotations for the image
        if not self.current_image_path or self.current_image_path not in self.annotations:
            QMessageBox.warning(self.ui, "Edit Annotation", "Cannot edit: No annotations found for the current image.")
            return
        current_annotations = self.annotations[self.current_image_path]
        if not (0 <= annotation_index < len(current_annotations)):
             QMessageBox.critical(self.ui, "Error", f"Annotation index {annotation_index} is out of sync with internal data.")
             return

        # --- Option 1: Simple - Switch to Select tool and enter editing ---
        self.select_annotation(annotation_index) # Selects and updates display
        self.set_current_tool('select')          # Ensure select tool is active
        self.enter_editing_mode_for_selected()   # Enter editing mode directly
        self.ui.annotation_image_view.setFocus() # Set focus to image view for keyboard events

        # --- Option 2: Dialog (kept commented for reference) ---
        # annotation = current_annotations[annotation_index]
        # current_class_id = annotation.get('class_id', 1)
        # edit_dialog = QDialog(self.ui)
        # ... (Dialog creation code from prompt) ...
        # edit_dialog.exec()


    def change_selected_annotation_class(self, class_id):
        """Change the class of the currently selected annotation."""
        if self.selected_annotation_index is None:
            logger.warning("Cannot change class: No annotation selected.")
            return
        if not self.current_image_path or self.current_image_path not in self.annotations:
             logger.error("Cannot change class: Annotation data missing.")
             return
        if not (0 <= self.selected_annotation_index < len(self.annotations[self.current_image_path])):
             logger.error(f"Cannot change class: Index {self.selected_annotation_index} out of bounds.")
             return

        # Update the annotation object
        self.annotations[self.current_image_path][self.selected_annotation_index]['class_id'] = class_id
        new_class_name = self.class_names.get(class_id, f"Unknown {class_id}")
        logger.info(f"Changed annotation {self.selected_annotation_index} class to {class_id} ({new_class_name})")
        
        # Mark annotations as modified
        self._annotations_modified = True

        # Update the current class selector to match
        self.current_class = class_id
        index = self.ui.class_selector.findData(class_id)
        if index >= 0:
            self.ui.class_selector.setCurrentIndex(index)

        # Update the list widget text
        self.load_annotations() # Reloads list with new names
        # Reselect the item in the list after reloading
        if 0 <= self.selected_annotation_index < self.ui.annotations_list.count():
            self.ui.annotations_list.setCurrentRow(self.selected_annotation_index)


        # Update display and save
        self.update_annotation_display()
        self.save_state()


    def delete_selected_annotation(self):
        """Delete the currently selected annotation (from select tool)."""
        if self.selected_annotation_index is None:
            logger.warning("Tried to delete but no annotation selected.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self.ui, "Confirm Deletion",
            f"Are you sure you want to delete annotation {self.selected_annotation_index + 1}?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply != QMessageBox.StandardButton.Yes:
            return

        self._delete_annotation_at_index(self.selected_annotation_index)
        # Deselect after deletion
        self.exit_editing_mode()


    def remove_annotation(self):
        """Remove the annotation(s) selected in the list widget."""
        selected_items = self.ui.annotations_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self.ui, "Remove Annotation", "Please select one or more annotations from the list to remove.")
            return

        # If multiple items are selected, confirm with a different message
        if len(selected_items) > 1:
            reply = QMessageBox.question(
                self.ui, "Confirm Multiple Removal",
                f"Are you sure you want to remove {len(selected_items)} annotations?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

            # Get all indices to delete, sorted in descending order to avoid index shifting
            indices_to_delete = []
            for item in selected_items:
                index = item.data(Qt.ItemDataRole.UserRole)
                if index is None:
                    index = self.ui.annotations_list.row(item)
                indices_to_delete.append(index)

            # Sort in descending order to avoid index shifting issues
            indices_to_delete.sort(reverse=True)

            # Delete each annotation
            for index in indices_to_delete:
                self._delete_annotation_at_index(index)

            # Exit editing mode if any of the deleted annotations was being edited
            if self.editing_mode and self.selected_annotation_index in indices_to_delete:
                self.exit_editing_mode()
            elif self.editing_mode:
                # Adjust selected index if removing items before it
                adjustment = sum(1 for idx in indices_to_delete if idx < self.selected_annotation_index)
                if adjustment > 0:
                    self.selected_annotation_index -= adjustment
        else:
            # Single item deletion (original behavior)
            list_item = selected_items[0]
            annotation_index = list_item.data(Qt.ItemDataRole.UserRole)
            if annotation_index is None:
                 annotation_index = self.ui.annotations_list.row(list_item)

            # Confirm deletion
            reply = QMessageBox.question(
                self.ui, "Confirm Removal",
                f"Are you sure you want to remove annotation {annotation_index + 1}?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

            self._delete_annotation_at_index(annotation_index)
            # Deselect if it was being edited
            if self.editing_mode and self.selected_annotation_index == annotation_index:
                self.exit_editing_mode()
            elif self.editing_mode and self.selected_annotation_index > annotation_index:
                 # Adjust selected index if removing an item before it
                 self.selected_annotation_index -= 1



    def _delete_annotation_at_index(self, index_to_delete):
         """Internal helper to delete annotation data and associated files."""
         if not self.current_image_path or self.current_image_path not in self.annotations:
              logger.error(f"Cannot delete annotation at index {index_to_delete}: No annotations for current image.")
              return
         current_annotations = self.annotations[self.current_image_path]
         if not (0 <= index_to_delete < len(current_annotations)):
              logger.error(f"Cannot delete annotation: Index {index_to_delete} out of bounds for image {self.current_image_path}.")
              return

         logger.info(f"Deleting annotation at index {index_to_delete} for image {self.current_image_path}")

         # --- Delete NPZ file ---
         adv_seg_state_dir = self._get_adv_seg_state_dir()
         if adv_seg_state_dir:
              dir_name = self._get_image_annotation_dir_name(self.current_image_path)
              npz_dir = Path(adv_seg_state_dir) / dir_name
              npz_filename = f"annotation_{index_to_delete}.npz"
              npz_filepath = npz_dir / npz_filename
              try:
                   if npz_filepath.is_file():
                        npz_filepath.unlink()
                        logger.info(f"Deleted annotation file: {npz_filepath}")
                   else:
                        # Check if file exists with different index due to previous deletions
                        # Re-listing and deleting the Nth file might be needed if indices aren't stable
                        logger.warning(f"Annotation file not found directly: {npz_filepath}. Manual cleanup might be needed if indices shifted.")
              except Exception as e:
                   logger.error(f"Error deleting annotation file {npz_filepath}: {e}")

         # --- Remove from memory ---
         del self.annotations[self.current_image_path][index_to_delete]
         
         # Mark annotations as modified
         self._annotations_modified = True

         # --- Update UI and Save ---
         self.load_annotations() # Reload list widget
         self.update_annotation_display() # Update image display
         self.save_state() # Persist the deletion


    # --- Dialog Launchers ---

    def show_dataset_preparation_dialog(self):
        """Show the dataset preparation dialog."""
        if not self.annotations:
            QMessageBox.warning(self.ui, "Dataset Preparation", "No annotations available. Please annotate images first.")
            return

        # Get absolute paths for the dialog
        abs_image_paths = {cp: self._get_absolute_path(cp) for cp in self.image_paths}

        # Convert annotations to use absolute paths as keys instead of canonical paths
        valid_annotations = {}
        for cp, annots in self.annotations.items():
            if cp in abs_image_paths:
                abs_path = abs_image_paths[cp]
                if os.path.exists(abs_path):  # Verify the file exists
                    valid_annotations[abs_path] = annots
                else:
                    logger.warning(f"Image file not found: {abs_path} (canonical: {cp})")

        if not valid_annotations:
             QMessageBox.warning(self.ui, "Dataset Preparation", "Annotations found, but corresponding image files seem missing.")
             return

        # Dynamic import to avoid circular dependencies if dialog imports handler
        try:
            from src.gui.dataset_preparation_dialog import DatasetPreparationDialog
            dialog = DatasetPreparationDialog(
                parent=self.ui,
                annotations=valid_annotations,  # Pass annotations with absolute paths as keys
                class_names=self.class_names
            )
            dialog.exec()
        except ImportError:
             logger.exception("Failed to import DatasetPreparationDialog.")
             QMessageBox.critical(self.ui, "Error", "Could not load the Dataset Preparation module.")
        except Exception as e:
             logger.exception("Error showing Dataset Preparation dialog.")
             QMessageBox.critical(self.ui, "Error", f"An error occurred: {e}")


    def show_class_management_dialog(self):
        """Show the class management dialog."""
        try:
            from src.gui.class_management_dialog import ClassManagementDialog
            dialog = ClassManagementDialog(self.ui, self.class_names, self.class_colors)
            dialog.class_data_changed.connect(self.update_class_data)
            dialog.exec() # exec_() is deprecated
            # No explicit accept check needed if signal handles update
        except ImportError:
             logger.exception("Failed to import ClassManagementDialog.")
             QMessageBox.critical(self.ui, "Error", "Could not load the Class Management module.")
        except Exception as e:
             logger.exception("Error showing Class Management dialog.")
             QMessageBox.critical(self.ui, "Error", f"An error occurred: {e}")


    def import_dataset(self):
        """Import a dataset (Placeholder)."""
        # TODO: Implement actual dataset import logic (COCO, YOLO, LabelMe etc.)
        # This would involve parsing annotation files, finding corresponding images,
        # normalizing paths, and populating self.image_paths and self.annotations.
        QMessageBox.information(self.ui, "Import Dataset", "Dataset import functionality is not yet implemented.")
        logger.info("Import dataset placeholder triggered.")


    def show_model_trainer_dialog(self):
        """Show the model trainer dialog."""
        try:
            from src.gui.model_trainer_dialog import ModelTrainerDialog
            # ModelTrainerDialog doesn't accept project_dir or class_names parameters
            dialog = ModelTrainerDialog(self.ui)
            dialog.exec()
        except ImportError:
             logger.exception("Failed to import ModelTrainerDialog.")
             QMessageBox.critical(self.ui, "Error", "Could not load the Model Training module.")
        except Exception as e:
             logger.exception("Error showing Model Training dialog.")
             QMessageBox.critical(self.ui, "Error", f"An error occurred: {e}")

    def show_model_evaluation_dialog(self):
        """Show the model evaluation dialog."""
        try:
            from src.gui.model_evaluation_dialog import ModelEvaluationDialog
             # ModelEvaluationDialog doesn't accept project_dir or class_names parameters
            dialog = ModelEvaluationDialog(self.ui)
            dialog.exec()
            logger.info("Displayed model evaluation dialog")
        except ImportError:
             logger.exception("Failed to import ModelEvaluationDialog.")
             QMessageBox.critical(self.ui, "Error", "Could not load the Model Evaluation module.")
        except Exception as e:
             logger.exception("Error showing Model Evaluation dialog.")
             QMessageBox.critical(self.ui, "Error", f"An error occurred: {e}")

    def refine_mask_with_sam(self):
        """Refine model predictions using MobileSAM for more precise masks."""
        # Check if we have segmentation previews (model inference results)
        if not hasattr(self, 'segmentation_previews') or not self.segmentation_previews:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "No model predictions available. Please run Model Inference first.")
            return

        # Check if we have annotations for the current image
        if not self.current_image_path or self.current_image_path not in self.annotations:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "No annotations available for the current image.")
            return

        # Check if SAM is initialized
        if not self.sam_handler or not self.sam_handler.predictor:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "SAM predictor is not initialized. Cannot refine masks.")
            return

        # Get the current image
        if self.current_image is None:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "No image loaded. Cannot refine masks.")
            return

        # Start timing the entire refinement process
        total_start_time = time.time()

        # Set the current image for SAM
        if not self.sam_handler.set_image(self.current_image):
            QMessageBox.critical(self.ui, "Refine Mask with SAM",
                                "Failed to set image for SAM prediction.")
            return

        # Get annotations for the current image
        annotations = self.annotations.get(self.current_image_path, [])

        # Filter annotations to only include those with bounding boxes (rectangle type or with 'box' field)
        box_annotations = []
        for i, annotation in enumerate(annotations):
            if annotation.get('type') == 'rectangle' and 'points' in annotation:
                # Rectangle annotation
                points = annotation.get('points')
                if len(points) == 2:
                    box_annotations.append((i, annotation))
            elif 'box' in annotation:
                # Annotation with explicit box field
                box_annotations.append((i, annotation))

        if not box_annotations:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "No bounding box annotations found for the current image.")
            return

        # Create progress dialog
        progress = QProgressDialog("Refining masks with SAM...", "Cancel", 0, len(box_annotations), self.ui)
        progress.setWindowTitle("Refine Mask with SAM")
        progress.setMinimumDuration(0)  # Show immediately
        progress.setValue(0)

        # Initialize dictionary to store refinement times
        if not hasattr(self, 'refinement_times'):
            self.refinement_times = {}

        # Dictionary to store per-mask refinement times for the current image
        mask_times = []

        # Process each bounding box annotation
        refined_count = 0
        for i, (idx, annotation) in enumerate(box_annotations):
            # Update progress
            progress.setValue(i)
            if progress.wasCanceled():
                break

            # Extract bounding box
            bbox = None
            if annotation.get('type') == 'rectangle' and 'points' in annotation:
                pt1, pt2 = annotation.get('points')
                x1, y1 = min(pt1[0], pt2[0]), min(pt1[1], pt2[1])
                x2, y2 = max(pt1[0], pt2[0]), max(pt1[1], pt2[1])
                bbox = [x1, y1, x2, y2]
            elif 'box' in annotation:
                bbox = annotation.get('box')

            if bbox is None:
                logger.warning(f"Could not extract bounding box from annotation {idx}")
                continue

            # Start timing for this mask
            mask_start_time = time.time()

            # Predict mask using SAM
            logger.info(f"Predicting SAM mask from box: {bbox}")
            masks, scores, _ = self.sam_handler.predict_from_box(bbox)

            # Calculate time for this mask
            mask_time = time.time() - mask_start_time
            mask_times.append(mask_time)
            logger.info(f"SAM mask prediction time for annotation {idx}: {mask_time:.3f}s")

            if masks is not None and len(masks) > 0:
                # Get the best mask
                best_mask_idx = np.argmax(scores)
                refined_mask = masks[best_mask_idx]

                # Create a new annotation with the refined mask
                refined_annotation = {
                    'type': 'mask',
                    'mask': refined_mask,
                    'class_id': annotation.get('class_id', 1),
                    'refined_from_box': True,  # Mark as refined
                    'refinement_time': mask_time  # Store the refinement time in the annotation
                }

                # Replace the original annotation with the refined one
                self.annotations[self.current_image_path][idx] = refined_annotation
                refined_count += 1
                logger.info(f"Refined annotation {idx} with SAM, score: {scores[best_mask_idx]:.4f}")
            else:
                logger.warning(f"SAM prediction failed for annotation {idx}")

        # Close progress dialog
        progress.setValue(len(box_annotations))

        # Calculate total refinement time
        total_refinement_time = time.time() - total_start_time

        # Store refinement times for the current image
        self.refinement_times[self.current_image_path] = {
            'total_time': total_refinement_time,
            'mask_times': mask_times,
            'average_time': sum(mask_times) / len(mask_times) if mask_times else 0,
            'count': len(mask_times)
        }

        logger.info(f"Total SAM refinement time for {self.current_image_path}: {total_refinement_time:.3f}s")
        logger.info(f"Average mask refinement time: {self.refinement_times[self.current_image_path]['average_time']:.3f}s")

        # Update UI
        if refined_count > 0:
            # Reload annotations list
            self.load_annotations()

            # Update annotation display
            self.update_annotation_display()

            # Save state
            self.save_state()

            # Create a new preview image with refined masks
            self.create_refined_preview()

            # Display the refined preview
            self.display_current_segmentation_preview()

            # Switch to the results tab
            if hasattr(self.ui, 'adv_seg_tabs'):
                self.ui.adv_seg_tabs.setCurrentIndex(1)

            # Show success message
            QMessageBox.information(self.ui, "Refine Mask with SAM",
                                   f"Successfully refined {refined_count} masks with SAM in {total_refinement_time:.3f}s " +
                                   f"(avg: {self.refinement_times[self.current_image_path]['average_time']:.3f}s per mask).")
        else:
            QMessageBox.warning(self.ui, "Refine Mask with SAM",
                               "No masks were refined. Check the logs for details.")

    def create_refined_preview(self):
        """Create a preview image with refined masks for the current image."""
        if self.current_image is None or not self.current_image_path:
            return

        # Start with a copy of the original image
        preview_img = self.current_image.copy()

        # Get annotations for the current image
        annotations = self.annotations.get(self.current_image_path, [])

        # Draw each annotation on the preview image
        for annotation in annotations:
            class_id = annotation.get('class_id', 1)
            # Get color for this class (convert from QColor to BGR)
            qcolor = self.class_colors.get(class_id, QColor(255, 0, 0))
            color_bgr = (qcolor.blue(), qcolor.green(), qcolor.red())

            # Draw mask if available
            if 'mask' in annotation and isinstance(annotation['mask'], np.ndarray):
                mask = annotation['mask']

                # Create colored overlay
                overlay = np.zeros_like(preview_img)
                overlay[mask > 0] = color_bgr

                # Apply overlay with transparency
                alpha = 0.5
                preview_img = cv2.addWeighted(preview_img, 1.0, overlay, alpha, 0)

                # Draw contour
                contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                cv2.drawContours(preview_img, contours, -1, color_bgr, 2)

        # Store the preview image
        if not hasattr(self, 'segmentation_previews'):
            self.segmentation_previews = {}

        # Convert BGR to RGB for consistent display
        preview_img_rgb = cv2.cvtColor(preview_img, cv2.COLOR_BGR2RGB)
        self.segmentation_previews[self.current_image_path] = preview_img_rgb
        self.segmentation_previews_are_rgb = True

        # Preserve inference time if it exists
        if hasattr(self, 'inference_times') and self.current_image_path in self.inference_times:
            # We don't need to do anything as the inference_times dictionary is separate
            pass

        logger.info(f"Created refined preview for {self.current_image_path}")

    def run_model_inference(self):
        """Run inference with a trained model."""
        if not self.image_paths:
            QMessageBox.warning(self.ui, "Model Inference", "No images loaded to run inference on.")
            return

        try:
            from src.gui.model_inference_dialog import ModelInferenceDialog
            # Provide absolute paths for the inference process
            abs_image_paths = [self._get_absolute_path(cp) for cp in self.image_paths]

            dialog = ModelInferenceDialog(
                parent=self.ui,
                image_paths=abs_image_paths,
                class_names=self.class_names
            )
            # Connect signals if the dialog emits results/previews
            dialog.inference_complete.connect(self.on_inference_complete)
            dialog.preview_results_ready.connect(self.on_preview_results_ready)
            dialog.exec()
        except ImportError:
             logger.exception("Failed to import ModelInferenceDialog.")
             QMessageBox.critical(self.ui, "Error", "Could not load the Model Inference module.")
        except Exception as e:
             logger.exception("Error showing Model Inference dialog.")
             QMessageBox.critical(self.ui, "Error", f"An error occurred: {e}")

    # --- Signal Handlers & Callbacks ---

    @Slot(dict)
    def on_inference_complete(self, inference_results):
        """Handle results after model inference completes."""
        # inference_results expected: {absolute_path: [annotation_dicts]}
        if not inference_results:
            logger.info("Inference completed with no results.")
            return

        logger.info(f"Processing inference results for {len(inference_results)} images.")
        added_count = 0
        img_count = 0

        for abs_path, new_annotations in inference_results.items():
            canonical_path = self._get_canonical_path(abs_path)
            if not canonical_path:
                logger.warning(f"Could not map inference result path {abs_path} to canonical path. Skipping.")
                continue

            if canonical_path not in self.annotations:
                self.annotations[canonical_path] = []

            # Add annotations, ensuring masks are generated if needed
            count_before = len(self.annotations[canonical_path])
            for annot in new_annotations:
                 if isinstance(annot, dict):
                      # Potentially generate mask here if inference only provided points
                      if annot.get('type') in ['polygon', 'rectangle'] and 'mask' not in annot:
                           # Need image context to create mask - tricky here.
                           # Maybe inference dialog should create masks?
                           # For now, just add the annotation as is.
                           pass
                      self.annotations[canonical_path].append(annot)
                 else:
                      logger.warning(f"Received invalid annotation format from inference: {type(annot)}")

            added_this_image = len(self.annotations[canonical_path]) - count_before
            if added_this_image > 0:
                 added_count += added_this_image
                 img_count += 1
                 logger.debug(f"Added {added_this_image} inferred annotations for {canonical_path}")

        # Update UI if the currently viewed image received annotations
        if self.current_image_path and self._get_absolute_path(self.current_image_path) in inference_results:
            logger.info("Reloading annotations for current image after inference.")
            self.load_annotations()

        # Enable the Refine Mask with SAM button if we have inference results
        if hasattr(self, 'segmentation_previews') and self.segmentation_previews:
            self.ui.refine_mask_sam_btn.setEnabled(True)
            self.ui.refine_mask_sam_btn.setToolTip("Refine model predictions using MobileSAM for more precise masks")
            logger.info("Enabled Refine Mask with SAM button after successful inference")

        if added_count > 0:
             self.save_state()
             QMessageBox.information(
                 self.ui, "Inference Complete",
                 f"Added {added_count} new annotations to {img_count} images."
             )
        else:
             QMessageBox.information(
                 self.ui, "Inference Complete",
                 "Inference finished, but no new annotations were added (possibly duplicates or errors)."
             )

    @Slot(dict)
    def on_preview_results_ready(self, preview_results):
        """Handle displayable preview results from model inference."""
         # preview_results expected: {absolute_path: {"visualization": cv2_image_rgb, "inference_time": float}}
        if not preview_results: return

        logger.info(f"Received segmentation preview results for {len(preview_results)} images.")

        # Store previews mapped by canonical path for persistence
        if not hasattr(self, 'segmentation_previews'):
            self.segmentation_previews = {}
            # Add a flag to track that preview images are in RGB format
            self.segmentation_previews_are_rgb = True
            # Add dictionary to store inference times
            self.inference_times = {}

        for abs_path, result in preview_results.items():
            canonical_path = self._get_canonical_path(abs_path)
            if canonical_path:
                # Handle both old format (direct image) and new format (dict with visualization and time)
                if isinstance(result, dict) and "visualization" in result:
                    self.segmentation_previews[canonical_path] = result["visualization"]
                    # Store inference time if available
                    if "inference_time" in result:
                        if not hasattr(self, 'inference_times'):
                            self.inference_times = {}
                        self.inference_times[canonical_path] = result["inference_time"]
                        logger.info(f"Inference time for {canonical_path}: {result['inference_time']:.3f}s")
                else:
                    # Old format - direct image
                    self.segmentation_previews[canonical_path] = result
            else:
                 logger.warning(f"Could not map preview path {abs_path} to canonical path.")

        # Display preview for the current image if available
        self.display_current_segmentation_preview()

        # Enable the Refine Mask with SAM button if we have preview results
        if self.segmentation_previews:
            self.ui.refine_mask_sam_btn.setEnabled(True)
            self.ui.refine_mask_sam_btn.setToolTip("Refine model predictions using MobileSAM for more precise masks")
            logger.info("Enabled Refine Mask with SAM button after receiving preview results")

        # Optionally switch to the results tab
        if hasattr(self.ui, 'adv_seg_tabs') and self.segmentation_previews:
             try:
                # Assuming index 1 is Segmentation Results. Verify this index.
                self.ui.adv_seg_tabs.setCurrentIndex(1)
             except IndexError:
                  logger.error("Cannot switch to Segmentation Results tab: Index out of range.")


    def display_current_segmentation_preview(self):
        """Display the stored segmentation preview for the current image."""
        if not hasattr(self, 'segmentation_previews') or not self.segmentation_previews:
             self.ui.segmentation_results_view.set_pixmap(QPixmap())
             # Disable the Refine Mask with SAM button if no previews available
             self.ui.refine_mask_sam_btn.setEnabled(False)
             self.ui.refine_mask_sam_btn.setToolTip("Run Model Inference first to enable this feature")
             return

        if not self.current_image_path:
             self.ui.segmentation_results_view.set_pixmap(QPixmap())
             # Disable the Refine Mask with SAM button if no current image
             self.ui.refine_mask_sam_btn.setEnabled(False)
             self.ui.refine_mask_sam_btn.setToolTip("No image selected")
             return

        # Get preview using the canonical path
        preview_img = self.segmentation_previews.get(self.current_image_path)

        if preview_img is not None:
            # Check if previews are in RGB format (from model_inference_dialog)
            is_rgb = hasattr(self, 'segmentation_previews_are_rgb') and self.segmentation_previews_are_rgb
            pixmap = convert_cvimage_to_qpixmap(preview_img, already_rgb=is_rgb)
            if not pixmap.isNull():
                 self.ui.segmentation_results_view.set_pixmap(pixmap, preserve_zoom=True, is_brush_update=False)
                 logger.debug(f"Displayed segmentation preview for {self.current_image_path} (RGB format: {is_rgb})")

                 # Display timing information if available
                 status_text = "Use mouse wheel to zoom, drag to pan, use scrollbars to navigate, or press Ctrl+0 to reset view"

                 # Add inference time if available
                 if hasattr(self, 'inference_times') and self.current_image_path in self.inference_times:
                     inference_time = self.inference_times[self.current_image_path]
                     status_text = f"Inference time: {inference_time:.3f}s | {status_text}"
                     logger.debug(f"Displayed inference time for {self.current_image_path}: {inference_time:.3f}s")

                 # Add SAM refinement time if available
                 if hasattr(self, 'refinement_times') and self.current_image_path in self.refinement_times:
                     refinement_data = self.refinement_times[self.current_image_path]
                     total_time = refinement_data['total_time']
                     avg_time = refinement_data['average_time']
                     count = refinement_data['count']

                     # Add refinement time to status text
                     status_text = f"SAM refinement: {total_time:.3f}s total, {avg_time:.3f}s avg ({count} masks) | {status_text}"
                     logger.debug(f"Displayed SAM refinement time for {self.current_image_path}: {total_time:.3f}s")

                 # Update the status label
                 if hasattr(self.ui, 'segmentation_instructions'):
                     self.ui.segmentation_instructions.setText(status_text)

                 # Enable the Refine Mask with SAM button for this image
                 self.ui.refine_mask_sam_btn.setEnabled(True)
                 self.ui.refine_mask_sam_btn.setToolTip("Refine model predictions using MobileSAM for more precise masks")
            else:
                 logger.error(f"Failed to convert segmentation preview to QPixmap for {self.current_image_path}")
                 self.ui.segmentation_results_view.set_pixmap(QPixmap())

                 # Disable the button if preview conversion failed
                 self.ui.refine_mask_sam_btn.setEnabled(False)
                 self.ui.refine_mask_sam_btn.setToolTip("Preview conversion failed")
        else:
             # No preview available for this image, clear the view
             self.ui.segmentation_results_view.set_pixmap(QPixmap())

             # Disable the button if no preview for current image
             self.ui.refine_mask_sam_btn.setEnabled(False)
             self.ui.refine_mask_sam_btn.setToolTip("No model predictions for this image. Run Model Inference first.")
             logger.debug(f"No segmentation preview available for {self.current_image_path}")


    @Slot(dict)
    def update_class_data(self, class_data):
        """Update class names and colors from the class management dialog."""
        if 'class_names' in class_data and 'class_colors' in class_data:
            self.class_names = class_data['class_names']
            self.class_colors = class_data['class_colors']
            logger.info("Updated class data from dialog.")
            self.update_class_selector()
            # Update annotations list display and image display
            self.load_annotations()
            self.save_state() # Persist class changes
        else:
            logger.warning("Received invalid class data from dialog.")


    def update_class_selector(self):
        """Update the class selector combobox."""
        current_id = self.current_class
        self.ui.class_selector.blockSignals(True) # Prevent triggering on_class_selected during update
        self.ui.class_selector.clear()

        if not self.class_names: # Handle empty class names case
            self.ui.class_selector.blockSignals(False)
            self.current_class = None # No class selectable
            return

        sorted_ids = sorted(self.class_names.keys())
        for class_id in sorted_ids:
            name = self.class_names[class_id]
            self.ui.class_selector.addItem(name, class_id) # Store ID in item data

        # Try to restore previous selection
        new_index = self.ui.class_selector.findData(current_id)
        if new_index >= 0:
            self.ui.class_selector.setCurrentIndex(new_index)
        elif sorted_ids: # If previous ID gone, select the first available one
            self.ui.class_selector.setCurrentIndex(0)
            self.current_class = self.ui.class_selector.itemData(0) # Update internal state
        else: # No classes left
             self.current_class = None

        self.ui.class_selector.blockSignals(False)
        # logger.debug(f"Class selector updated. Current class: {self.current_class}")


    def on_class_selected(self, index):
        """Handle class selection change from the combobox."""
        if index < 0: return
        class_id = self.ui.class_selector.itemData(index)
        if class_id is not None:
            self.current_class = class_id
            class_name = self.class_names.get(class_id, "Unknown")
            logger.info(f"Current annotation class set to: {class_id} ({class_name})")
            # If editing an annotation, maybe update its class immediately?
            # if self.editing_mode and self.selected_annotation_index is not None:
            #     self.change_selected_annotation_class(class_id)


    def on_key_press(self, event: QKeyEvent):
        """Handle keyboard shortcuts."""
        key = event.key()
        modifiers = event.modifiers()

        # --- Accept/Reject ---
        if key == Qt.Key.Key_Return or key == Qt.Key.Key_Enter:
            if self.ui.accept_sam_btn.isEnabled():
                logger.debug("Accepting mask via Enter key.")
                self.accept_mask()
                event.accept()
                return
            elif self.editing_mode:
                 logger.debug("Finalizing annotation edit via Enter key.")
                 self.update_annotation_from_editing_points()
                 # Optionally exit editing mode after Enter?
                 # self.exit_editing_mode()
                 event.accept()
                 return

        if key == Qt.Key.Key_Escape:
            if self.ui.reject_sam_btn.isEnabled():
                logger.debug("Rejecting mask via Escape key.")
                self.reject_mask()
                event.accept()
                return
            elif self.drawing: # Cancel drawing polygon/rectangle/box
                 logger.debug("Cancelling drawing via Escape key.")
                 self.drawing = False
                 self.drawing_sam_bbox = False
                 self.points = []
                 self.start_point = None
                 self.end_point = None
                 self.update_annotation_display()
                 event.accept()
                 return
            elif self.editing_mode:
                 logger.debug("Exiting editing mode via Escape key.")
                 self.exit_editing_mode()
                 event.accept()
                 return
            elif self.current_tool: # Deselect current tool
                 logger.debug(f"Deactivating tool {self.current_tool} via Escape key.")
                 self.set_current_tool(self.current_tool) # Toggle off
                 event.accept()
                 return

        # --- Tool Activation ---
        if key == Qt.Key.Key_S and not modifiers:
             logger.debug("Toggling Select tool via S key.")
             self.set_current_tool('select')
             event.accept()
             return
        if key == Qt.Key.Key_P and not modifiers:
             logger.debug("Toggling Positive Point tool via P key.")
             self.toggle_point_prompt()
             event.accept()
             return
        if key == Qt.Key.Key_N and not modifiers:
             logger.debug("Toggling Negative Point tool via N key.")
             self.toggle_negative_point_prompt()
             event.accept()
             return
        if key == Qt.Key.Key_B and not modifiers:
             logger.debug("Toggling Brush tool via B key.")
             self.set_current_tool('brush')
             event.accept()
             return
        if key == Qt.Key.Key_E and not modifiers:
             logger.debug("Toggling Erase tool via E key.")
             self.set_current_tool('erase')
             event.accept()
             return
        # Add keys for Polygon (G?), Rectangle (R?), Magic Wand (W?) if desired

        # --- Other Actions ---
        if key == Qt.Key.Key_R and not modifiers: # Changed Rectangle key, use R for Reset points
             if self.ui.reset_points_btn.isEnabled():
                 logger.debug("Resetting points via R key.")
                 self.reset_points()
                 event.accept()
                 return

        if key == Qt.Key.Key_Delete or key == Qt.Key.Key_Backspace:
             if self.current_tool == 'select' and self.selected_annotation_index is not None:
                 logger.debug("Deleting selected annotation via Delete/Backspace key.")
                 self.delete_selected_annotation() # Will ask for confirmation
                 event.accept()
                 return

        # --- Brush Size ---
        if key == Qt.Key.Key_BracketRight or key == Qt.Key.Key_Plus or key == Qt.Key.Key_Equal:
             if self.current_tool in ['brush', 'erase']:
                  current_value = self.ui.brush_size_slider.value()
                  new_value = min(current_value + 5, self.ui.brush_size_slider.maximum())
                  if new_value != current_value:
                       self.ui.brush_size_slider.setValue(new_value)
                       logger.debug(f"Increased brush size to {new_value}")
                  event.accept()
                  return
        if key == Qt.Key.Key_BracketLeft or key == Qt.Key.Key_Minus:
             if self.current_tool in ['brush', 'erase']:
                  current_value = self.ui.brush_size_slider.value()
                  new_value = max(current_value - 5, self.ui.brush_size_slider.minimum())
                  if new_value != current_value:
                       self.ui.brush_size_slider.setValue(new_value)
                       logger.debug(f"Decreased brush size to {new_value}")
                  event.accept()
                  return

        # Allow default processing for other keys (e.g., panning/zooming in view)
        # Call base class implementation if event not handled
        # This depends on the actual base class of annotation_image_view
        # Assuming it has a keyPressEvent we can call:
        # super(type(self.ui.annotation_image_view), self.ui.annotation_image_view).keyPressEvent(event)
        # If the view doesn't have a specific base class handling keys, just pass
        pass


    # --- State Management ---

    def save_state(self):
        """
        This method is now disabled in favor of manual save/load.
        Use save_annotations_to_file instead.
        """
        logger.info("Automatic state saving is disabled. Use 'Save Annotations' button instead.")
        return False


    def _cleanup_orphaned_annotation_dirs(self, adv_seg_state_dir, valid_canonical_paths):
         """Removes NPZ directories that don't correspond to current annotations."""
         if not os.path.isdir(adv_seg_state_dir): return

         # Generate valid directory names from canonical paths
         valid_dir_names = {self._get_image_annotation_dir_name(cp) for cp in valid_canonical_paths}
         logger.debug(f"Valid directory names: {valid_dir_names}")

         try:
              for item_name in os.listdir(adv_seg_state_dir):
                   item_path = Path(adv_seg_state_dir) / item_name
                   # Check if it's a directory and looks like our NPZ storage format
                   if item_path.is_dir() and not item_name.startswith(('annotation_', '.', 'state_')): # Basic check to avoid deleting state files etc.
                        # First check if the directory name is in our valid list
                        if item_name in valid_dir_names:
                            logger.debug(f"Directory {item_name} is valid, keeping it")
                            continue

                        # If not in the valid list, try to match it to an image path
                        matched_path = self._get_image_path_from_dir_name(item_name)
                        if matched_path is not None and matched_path in valid_canonical_paths:
                            logger.info(f"Directory {item_name} matched to valid image path {matched_path}, keeping it")
                            continue

                        # If we get here, the directory is orphaned and should be removed
                        logger.warning(f"Removing orphaned annotation directory: {item_path}")
                        try:
                             import shutil
                             shutil.rmtree(item_path)
                        except Exception as e:
                             logger.error(f"Error removing orphaned directory {item_path}: {e}")
         except Exception as e:
              logger.error(f"Error during orphaned directory cleanup: {e}")


    def load_state(self):
        """
        This method is now disabled in favor of manual load.
        Use load_annotations_from_file instead.
        """
        logger.info("Automatic state loading is disabled. Use 'Load Annotations' button instead.")
        return False


    def clear_state(self):
        """Clear all internal state and potentially state files."""
        logger.info("Clearing trainable segmentation state.")

        # Clear internal state variables
        self.current_image = None
        self.current_image_path = None
        self.current_image_index = -1
        self.image_paths = []
        self.image_infos = []
        self.annotations = {}
        self.current_annotation = None
        self.current_tool = None
        self.drawing = False
        self.points = []
        self.brush_mask = None
        self.last_brush_point = None
        self.drawing_sam_bbox = False
        self.start_point = None
        self.end_point = None
        self.current_mask = None
        self.point_prompts = []
        self.is_adding_points = False
        self.editing_mode = False
        self.selected_annotation_index = None
        self.editing_points = []
        self.selected_point_index = None
        self.hover_point_index = None
        self.drag_point = False
        self._annotations_modified = False
        if hasattr(self, 'segmentation_previews'): self.segmentation_previews = {}

        # Reset class defaults
        self.class_names = {1: "Class 1", 2: "Class 2", 3: "Class 3"}
        self.class_colors = {1: QColor(255,0,0), 2: QColor(0,255,0), 3: QColor(0,0,255)}
        self.current_class = 1

        # Clear UI elements
        self.clear_gallery() # Clears gallery UI and its internal lists
        self.ui.annotations_list.clear()
        # Use set_pixmap with empty pixmap instead of clear for SynchronizedImageView
        self.ui.annotation_image_view.set_pixmap(QPixmap())
        self.ui.segmentation_results_view.set_pixmap(QPixmap())
        self.update_class_selector()
        self.ui.accept_sam_btn.setEnabled(False)
        self.ui.reject_sam_btn.setEnabled(False)
        self.ui.reset_points_btn.setEnabled(False)
        # Deselect all tool buttons
        for btn in [self.ui.select_tool_btn, self.ui.polygon_tool_btn, self.ui.rectangle_tool_btn,
                   self.ui.point_prompt_tool_btn, self.ui.negative_point_prompt_tool_btn,
                   self.ui.brush_tool_btn, self.ui.erase_tool_btn,
                   self.ui.magic_wand_tool_btn]:
            btn.setChecked(False)


        # Optional: Clear physical state files
        # Be cautious with this, maybe make it user-configurable?
        # For now, just clearing in-memory state. If a new project is loaded,
        # the SessionState dir should change anyway. If the user wants to clear
        # within the *same* project, they can delete annotations manually.

        # adv_seg_state_dir = self._get_adv_seg_state_dir()
        # if adv_seg_state_dir and os.path.exists(adv_seg_state_dir):
        #     logger.warning(f"Attempting to clear state files in {adv_seg_state_dir}")
        #     try:
        #         import shutil
        #         # Remove contents, not the dir itself
        #         for item in os.listdir(adv_seg_state_dir):
        #             item_path = os.path.join(adv_seg_state_dir, item)
        #             if os.path.isfile(item_path) or os.path.islink(item_path):
        #                 os.unlink(item_path)
        #             elif os.path.isdir(item_path):
        #                 shutil.rmtree(item_path)
        #         logger.info("Cleared contents of trainable segmentation state directory.")
        #     except Exception as e:
        #         logger.error(f"Error clearing trainable segmentation state directory contents: {e}")

        logger.info("Internal trainable segmentation state has been reset.")


    def save_annotations_to_file(self):
        """Save annotations to a JSON file with NPZ files for masks."""
        if not self.image_paths:
            QMessageBox.information(self.ui, "Save Annotations", "No images loaded. Nothing to save.")
            return

        # Get the trainable segmentation state directory as the default save location
        default_dir = self._get_adv_seg_state_dir()
        if not default_dir:
            default_dir = os.path.expanduser("~")  # Fallback to user's home directory

        # Create a default filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"annotations_{timestamp}.json"
        default_path = os.path.join(default_dir, default_filename)

        # Show file dialog to select save location
        file_path, _ = QFileDialog.getSaveFileName(
            self.ui,
            "Save Annotations",
            default_path,
            "JSON Files (*.json)"
        )

        if not file_path:
            logger.info("Save annotations cancelled by user")
            return

        # Create the directory for NPZ files if it doesn't exist
        npz_dir = os.path.splitext(file_path)[0] + "_masks"
        os.makedirs(npz_dir, exist_ok=True)

        # Prepare annotations for saving
        processed_annotations = {}
        save_errors = False

        # Show progress dialog
        progress = QProgressDialog("Saving annotations...", "Cancel", 0, len(self.annotations), self.ui)
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(500)  # Only show if operation takes more than 500ms

        # Process annotations for each image
        for i, (canonical_path, annotations_list) in enumerate(self.annotations.items()):
            progress.setValue(i)
            if progress.wasCanceled():
                break

            image_annotations_for_json = []

            # Get safe directory name for NPZ files based on canonical path
            npz_dir_name = self._get_image_annotation_dir_name(canonical_path)
            image_npz_dir = os.path.join(npz_dir, npz_dir_name)
            os.makedirs(image_npz_dir, exist_ok=True)

            for j, annotation in enumerate(annotations_list):
                if not isinstance(annotation, dict):
                    logger.error(f"Skipping invalid annotation (not dict) for {canonical_path} at index {j}")
                    save_errors = True
                    continue

                annotation_copy = annotation.copy()  # Work on a copy

                # Save mask if present
                if 'mask' in annotation_copy and isinstance(annotation_copy['mask'], np.ndarray):
                    mask_data = annotation_copy.pop('mask')  # Remove mask from copy
                    npz_filename = f"annotation_{j}.npz"
                    npz_filepath = os.path.join(image_npz_dir, npz_filename)

                    try:
                        np.savez_compressed(npz_filepath, mask=mask_data)
                        # Add reference to the JSON data
                        annotation_copy['mask_file'] = npz_filename
                        annotation_copy['mask_dir'] = npz_dir_name
                    except Exception as e:
                        logger.error(f"Error saving mask to {npz_filepath}: {e}")
                        save_errors = True
                elif 'mask' in annotation_copy:
                    # If mask exists but isn't ndarray, log warning and remove invalid entry
                    logger.warning(f"Annotation for {canonical_path} index {j} has non-numpy mask (type: {type(annotation_copy['mask'])}). Removing.")
                    del annotation_copy['mask']

                image_annotations_for_json.append(annotation_copy)

            if image_annotations_for_json:  # Only add if there are valid annotations
                processed_annotations[canonical_path] = image_annotations_for_json

        progress.setValue(len(self.annotations))

        # Prepare final state dictionary
        class_names_serializable = {str(k): v for k, v in self.class_names.items()}
        class_colors_serializable = {str(k): v.name() for k, v in self.class_colors.items()}

        state_data = {
            'metadata': {
                'version': '1.0',
                'timestamp': datetime.now().isoformat(),
                'image_count': len(processed_annotations),
                'annotation_count': sum(len(anns) for anns in processed_annotations.values())
            },
            'annotations': processed_annotations,
            'class_names': class_names_serializable,
            'class_colors': class_colors_serializable
        }

        # Save JSON file
        try:
            with open(file_path, 'w') as f:
                json.dump(state_data, f, indent=2)

            if save_errors:
                QMessageBox.warning(
                    self.ui,
                    "Save Annotations",
                    f"Annotations saved with some errors. Check the log for details.\n\nSaved to: {file_path}"
                )
            else:
                QMessageBox.information(
                    self.ui,
                    "Save Annotations",
                    f"Annotations saved successfully to:\n{file_path}\n\nMask files saved to:\n{npz_dir}"
                )
            logger.info(f"Annotations saved to {file_path}")
            return True
        except Exception as e:
            QMessageBox.critical(
                self.ui,
                "Save Annotations Error",
                f"Error saving annotations: {str(e)}"
            )
            logger.error(f"Error saving annotations to {file_path}: {e}")
            return False

    def load_annotations_from_file(self):
        """Load annotations from a JSON file with NPZ files for masks."""
        # Show file dialog to select file to load
        file_path, _ = QFileDialog.getOpenFileName(
            self.ui,
            "Load Annotations",
            self._get_adv_seg_state_dir() or os.path.expanduser("~"),
            "JSON Files (*.json)"
        )

        if not file_path:
            logger.info("Load annotations cancelled by user")
            return

        # Load JSON data
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            if not isinstance(data, dict) or 'annotations' not in data:
                QMessageBox.critical(
                    self.ui,
                    "Load Annotations Error",
                    "Invalid annotation file format. The file does not contain valid annotations."
                )
                return False

            # Extract class information
            if 'class_names' in data and 'class_colors' in data:
                try:
                    # Update class names and colors
                    self.class_names = {int(k) if k.isdigit() else k: v for k, v in data['class_names'].items()}
                    self.class_colors = {int(k) if k.isdigit() else k: QColor(v) for k, v in data['class_colors'].items()}

                    # Ensure default colors exist if needed
                    for cid in self.class_names:
                        if cid not in self.class_colors:
                            self.class_colors[cid] = QColor(np.random.randint(0,256), np.random.randint(0,256), np.random.randint(0,256))

                    self.update_class_selector()  # Update UI
                except Exception as e:
                    logger.error(f"Error loading class definitions: {e}")
                    # Continue with default classes

            # Process annotations
            annotations_data = data['annotations']
            present_images = []
            missing_images = []

            # Create a mapping of filenames to loaded image paths
            filename_to_path = {}
            for img_path in self.image_paths:
                filename = Path(img_path).name
                filename_to_path[filename] = img_path

            # Check which images are present and which are missing
            for image_path in annotations_data.keys():
                if image_path in self.image_paths:
                    present_images.append(image_path)
                else:
                    # Try to match by filename
                    filename = Path(image_path).name
                    if filename in filename_to_path:
                        # Map the annotation to the current path
                        present_images.append(filename_to_path[filename])
                    else:
                        missing_images.append(image_path)

            # If there are missing images, show a simple notification
            if missing_images:
                # Just show a message about the missing images
                QMessageBox.information(
                    self.ui,
                    "Missing Images",
                    f"Found annotations for {len(missing_images)} images that aren't currently loaded.\n"
                    f"These annotations will be discarded."
                )

                # Discard annotations for missing images
                for path in missing_images:
                    if path in self.pending_annotations:
                        del self.pending_annotations[path]

            # Load annotations for present images
            self._load_annotations_for_present_images(present_images, annotations_data, file_path)

            # Update UI
            self.rebuild_gallery()
            if self.current_image_index != -1:
                self.load_annotations()  # Reload annotations for current image
            elif self.image_paths:
                self.select_image(0)  # Select first image if none selected

            QMessageBox.information(
                self.ui,
                "Load Annotations",
                f"Loaded annotations for {len(present_images)} images."
            )
            return True

        except Exception as e:
            QMessageBox.critical(
                self.ui,
                "Load Annotations Error",
                f"Error loading annotations: {str(e)}"
            )
            logger.error(f"Error loading annotations from {file_path}: {e}")
            return False

    # The _attempt_to_load_missing_images method has been removed as we now discard annotations for missing images

    def _load_annotations_for_present_images(self, present_images, annotations_data, annotations_file_path):
        """Load annotations for images that are currently present."""
        # Get the masks directory
        masks_dir = os.path.splitext(annotations_file_path)[0] + "_masks"

        # Create progress dialog
        progress = QProgressDialog("Loading annotations...", "Cancel", 0, len(present_images), self.ui)
        progress.setWindowModality(Qt.WindowModal)

        load_errors = False

        for i, target_path in enumerate(present_images):
            progress.setValue(i)
            if progress.wasCanceled():
                break

            # Find the corresponding annotations in the data
            annotations_found = False

            # First try direct match
            if target_path in annotations_data:
                source_path = target_path
                annotations_found = True
            else:
                # Try to match by filename
                target_filename = Path(target_path).name
                for source_path in annotations_data:
                    if Path(source_path).name == target_filename:
                        annotations_found = True
                        break

            if not annotations_found:
                logger.warning(f"Could not find annotations for {target_path}")
                continue

            # Process annotations for this image
            annotations_list = annotations_data[source_path]
            current_image_annotations = []

            for j, annotation_data in enumerate(annotations_list):
                if not isinstance(annotation_data, dict):
                    logger.warning(f"Skipping invalid annotation data (not dict) for {source_path} at index {j}")
                    load_errors = True
                    continue

                processed_annotation = annotation_data.copy()  # Work on copy

                # Load mask if reference exists
                if 'mask_file' in processed_annotation and 'mask_dir' in processed_annotation:
                    mask_filename = processed_annotation['mask_file']
                    mask_dir_name = processed_annotation['mask_dir']

                    # Try to load the mask from the masks directory
                    npz_filepath = os.path.join(masks_dir, mask_dir_name, mask_filename)

                    try:
                        if os.path.isfile(npz_filepath):
                            with np.load(npz_filepath) as data:
                                processed_annotation['mask'] = data['mask']
                            # Remove references after successful load
                            del processed_annotation['mask_file']
                            del processed_annotation['mask_dir']
                        else:
                            logger.warning(f"Mask file not found: {npz_filepath}")
                            # Keep the references in case we need to regenerate the mask
                    except Exception as e:
                        logger.error(f"Error loading mask from {npz_filepath}: {e}")
                        # Keep the references in case we need to regenerate the mask

                current_image_annotations.append(processed_annotation)

            # Add the loaded annotations to our internal state
            self.annotations[target_path] = current_image_annotations
            logger.info(f"Loaded {len(current_image_annotations)} annotations for {target_path}")

            # Regenerate masks if needed
            if any('mask' not in ann and ann.get('type') in ['polygon', 'rectangle'] and 'points' in ann
                  for ann in current_image_annotations):
                self._regenerate_masks_from_points(target_path)

        progress.setValue(len(present_images))

        if load_errors:
            logger.warning("Some errors occurred while loading annotations")

    def quick_save_annotations(self):
        """Quickly save all annotations to a predefined directory without user dialog."""
        if not self.image_paths:
            QMessageBox.warning(self.ui, "Warning", "No images loaded. Nothing to save.")
            return

        # Get the default directory
        default_dir = self._get_adv_seg_state_dir()
        if not default_dir:
            default_dir = os.path.join(os.path.expanduser("~"), "PetroSEG_QuickSave")
        
        # Create quick save subdirectory
        quick_save_dir = os.path.join(default_dir, "quick_save")
        os.makedirs(quick_save_dir, exist_ok=True)

        # Create a default filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"quick_save_{timestamp}.json"
        file_path = os.path.join(quick_save_dir, default_filename)

        # Create the subdirectory with the same name as the JSON file (without extension)
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        save_dir = os.path.join(os.path.dirname(file_path), base_name + "_masks")
        os.makedirs(save_dir, exist_ok=True)

        # Prepare the metadata
        metadata = {
            "timestamp": timestamp,
            "images": [],
            "version": "1.0"
        }

        # Save annotations for each image
        success_count = 0
        error_count = 0

        for image_path in self.image_paths:
            try:
                if image_path not in self.annotations or not self.annotations[image_path]:
                    continue

                # Create a safe filename from the image path
                image_name = os.path.basename(image_path)
                safe_name = "".join(c for c in image_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                
                # Save each annotation for this image
                image_annotations = []
                for i, annotation in enumerate(self.annotations[image_path]):
                    annotation_data = annotation.copy()
                    
                    # Handle mask data separately
                    if 'mask' in annotation_data:
                        mask_filename = f"{safe_name}_annotation_{i}_mask.npz"
                        mask_path = os.path.join(save_dir, mask_filename)
                        np.savez_compressed(mask_path, mask=annotation_data['mask'])
                        annotation_data['mask_file'] = mask_filename
                        del annotation_data['mask']  # Remove mask from JSON data
                    
                    image_annotations.append(annotation_data)

                if image_annotations:
                    metadata["images"].append({
                        "image_path": image_path,
                        "annotations": image_annotations,
                        "annotation_count": len(image_annotations)
                    })
                    success_count += 1

            except Exception as e:
                logger.error(f"Failed to save annotations for {image_path}: {e}")
                error_count += 1

        # Save the metadata JSON file
        try:
            with open(file_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            # Show success message
            if success_count > 0:
                QMessageBox.information(
                    self.ui,
                    "Quick Save Complete",
                    f"Annotations saved to {quick_save_dir}.\n\n"
                    f"Saved annotations for {success_count} images.\n"
                    f"Use Quick Load to load them."
                )
            else:
                QMessageBox.information(
                    self.ui,
                    "Quick Save Complete",
                    "No annotations were saved. All images had no annotations."
                )

        except Exception as e:
            QMessageBox.critical(
                self.ui,
                "Quick Save Error",
                f"Failed to save metadata file: {str(e)}"
            )

    def quick_load_annotations(self):
        """Quickly load the most recent annotations from the predefined directory."""
        if not self.image_paths:
            QMessageBox.warning(self.ui, "Warning", "No images loaded. Please load images first.")
            return

        # Get the default directory
        default_dir = self._get_adv_seg_state_dir()
        if not default_dir:
            default_dir = os.path.join(os.path.expanduser("~"), "PetroSEG_QuickSave")
        
        quick_save_dir = os.path.join(default_dir, "quick_save")

        # Check if the directory exists
        if not os.path.exists(quick_save_dir) or not os.path.isdir(quick_save_dir):
            QMessageBox.warning(self.ui, "Warning", f"No saved annotations found in {quick_save_dir}")
            return

        # Find the most recent JSON file
        json_files = [f for f in os.listdir(quick_save_dir) if f.endswith('.json')]
        if not json_files:
            QMessageBox.warning(self.ui, "Warning", f"No annotation files found in {quick_save_dir}")
            return

        # Sort by modification time (most recent first)
        json_files.sort(key=lambda x: os.path.getmtime(os.path.join(quick_save_dir, x)), reverse=True)
        most_recent_file = json_files[0]
        file_path = os.path.join(quick_save_dir, most_recent_file)

        # Load the JSON metadata file
        try:
            with open(file_path, 'r') as f:
                metadata = json.load(f)

            # Check if the metadata has the expected structure
            if not isinstance(metadata, dict) or "images" not in metadata:
                QMessageBox.critical(self.ui, "Error", "Invalid metadata file format. Missing 'images' key.")
                return

            # Get the directory containing the mask files
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            masks_dir = os.path.join(os.path.dirname(file_path), base_name + "_masks")

            # Check if the masks directory exists
            if not os.path.exists(masks_dir) or not os.path.isdir(masks_dir):
                QMessageBox.critical(self.ui, "Error", f"Masks directory not found: {masks_dir}")
                return

            # Get list of currently loaded images
            current_images = set(self.image_paths)
            
            # Find images that are present in both the metadata and current session
            present_images = []
            for image_data in metadata["images"]:
                image_path = image_data["image_path"]
                if image_path in current_images:
                    present_images.append(image_data)

            if not present_images:
                QMessageBox.warning(self.ui, "Warning", "No matching images found between saved annotations and currently loaded images.")
                return

            # Load annotations for present images
            success_count = 0
            error_count = 0

            for image_data in present_images:
                try:
                    image_path = image_data["image_path"]
                    annotations = image_data["annotations"]
                    
                    # Restore annotations with masks
                    restored_annotations = []
                    for annotation in annotations:
                        restored_annotation = annotation.copy()
                        
                        # Load mask if available
                        if 'mask_file' in annotation:
                            mask_path = os.path.join(masks_dir, annotation['mask_file'])
                            if os.path.exists(mask_path):
                                mask_data = np.load(mask_path)
                                restored_annotation['mask'] = mask_data['mask']
                            del restored_annotation['mask_file']  # Remove file reference
                        
                        restored_annotations.append(restored_annotation)
                    
                    # Set annotations for this image
                    self.annotations[image_path] = restored_annotations
                    success_count += 1

                except Exception as e:
                    logger.error(f"Failed to load annotations for {image_path}: {e}")
                    error_count += 1

            # If we're currently viewing an image, reload its annotations
            if self.current_image_path and self.current_image_path in [img["image_path"] for img in present_images]:
                self.load_annotations()
                self.display_current_segmentation_preview()

            # Update the gallery to reflect loaded annotations
            if hasattr(self.ui, 'adv_seg_gallery') and hasattr(self.ui.adv_seg_gallery, 'update_display'):
                self.ui.adv_seg_gallery.update_display()

            # Show completion message
            if success_count > 0:
                QMessageBox.information(
                    self.ui,
                    "Quick Load Complete",
                    f"Loaded annotations for {success_count} images from {most_recent_file}."
                )
            else:
                QMessageBox.warning(
                    self.ui,
                    "Quick Load Warning",
                    "No annotations were loaded."
                )

        except Exception as e:
            QMessageBox.critical(
                self.ui,
                "Quick Load Error",
                f"Failed to load annotations: {str(e)}"
            )
            logger.error(f"Failed to load annotations: {e}")

    def apply_theme(self, theme_name="dark", style_params=None):
        """Apply theme styles (placeholder)."""
        # This function would typically apply stylesheets based on the theme.
        # The example provided in the original code is a good starting point.
        # For brevity in this refactoring, the styling logic is omitted,
        # but you would place your QSS generation and setStyleSheet calls here.
        logger.debug(f"Applying theme '{theme_name}' to Advanced Segmentation page (styling logic omitted in refactor).")
        # Example of applying style (repeat for other widgets):
        # is_dark = theme_name == "dark"
        # button_style = f"QPushButton {{ background-color: {'#333' if is_dark else '#EEE'}; color: {'white' if is_dark else 'black'}; }}"
        # self.ui.select_tool_btn.setStyleSheet(button_style)
        # ... apply to other relevant UI elements ...


# ============================================================================
# NOTE: The SessionState class definition and image_utils functions were assumed
#       to be defined elsewhere as per the original prompt structure.
#       Make sure those modules (`src.utils.session_state`, `src.utils.image_utils`,
#       `src.utils.json_utils`) exist and are correct in your project.
#
#       Specifically, ensure `SessionState.save_state` and `load_state` handle

    def clear_advanced_segmentation_gallery(self):
        """Clears all images from the trainable segmentation gallery."""
        if hasattr(self.ui, 'adv_seg_gallery'):
            self.ui.adv_seg_gallery.clear_images()
            
            # Reset image state variables
            self.current_image = None
            self.current_image_path = None
            self.current_image_index = -1
            self.image_paths = []
            self.image_infos = []
            
            # Clear annotations
            self.annotations = {}
            self.current_annotation = None
            
            # Clear the image viewers
            if hasattr(self.ui, 'adv_seg_image_viewer') and hasattr(self.ui.adv_seg_image_viewer, 'scene'):
                self.ui.adv_seg_image_viewer.scene().clear()
            
            # Clear the annotation image view as well
            if hasattr(self.ui, 'annotation_image_view'):
                self.ui.annotation_image_view.set_pixmap(QPixmap())
                logger.info("Advanced segmentation annotation image view cleared")
            
            logger.info("Advanced segmentation gallery and state cleared successfully")
#       the JSON file operations correctly (timestamping, pointer file, cleanup)
#       within the main state directory (e.g., `project_data/state/`).
#       The handler now relies on this utility for the core JSON state management.
# ============================================================================
