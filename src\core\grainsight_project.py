"""
VisionLab Project Module

This module provides the VisionLabProject class for managing VisionLab Ai projects
in the .vlp (Vision Lab Project) format, which stores all project data in a single file.
"""

import os
import json
import shutil
import logging
import zipfile
import tempfile
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import numpy as np
import pandas as pd
import pickle
from pathlib import Path

from src.core.project_data import Project, ImageInfo

logger = logging.getLogger(__name__)

class VisionLabProject:
    """Class to manage a VisionLab Ai project in .vlp format.

    This class extends the functionality of the Project class to support
    saving all project data in a single .vlp file, which is actually a zip file
    containing all project data including images, results, and state information.
    """

    def __init__(self, project_file: str = None):
        """Initialize a new VisionLabProject.

        Args:
            project_file: Path to an existing .vlp project file to load, or None to create a new project.
        """
        self.project_file = project_file
        self.name = os.path.basename(project_file).replace('.vlp', '') if project_file else "New Project"
        self.description = ""
        self.created_at = datetime.now().isoformat()
        self.modified_at = self.created_at
        self.images = {}  # Dictionary of image_id -> ImageInfo
        self.temp_dir = None  # Temporary directory for extracted project files

        # State dictionaries for different pages
        self.grain_analysis_states = {}
        self.image_processing_states = {}
        self.trainable_segmentation_states = {}
        self.image_enhancement_states = {}
        self.point_counting_states = {}
        self.ai_assistant_states = {}

        # If a project file is provided, load it
        if project_file and os.path.exists(project_file):
            self.load(project_file)

    def _create_project_directory(self):
        """Create a persistent directory for project files.

        This creates a directory next to the .vlp file to store the extracted project data.
        This ensures state is preserved between sessions.
        """
        if not self.project_file:
            # If no project file is specified, use a temporary directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                # Clean up existing temp directory
                shutil.rmtree(self.temp_dir)

            # Create a new temp directory
            self.temp_dir = tempfile.mkdtemp(prefix="visionlab_ai_")
        else:
            # Create a persistent directory next to the .vlp file
            project_dir = os.path.splitext(self.project_file)[0] + "_data"

            # Create the directory if it doesn't exist
            if not os.path.exists(project_dir):
                os.makedirs(project_dir, exist_ok=True)

            self.temp_dir = project_dir
            logger.info(f"Using persistent project directory: {self.temp_dir}")

        # Create simplified project structure in the directory
        # Only create essential directories
        os.makedirs(os.path.join(self.temp_dir, "images"), exist_ok=True)  # For storing project images
        os.makedirs(os.path.join(self.temp_dir, "state"), exist_ok=True)  # Main state directory

        # Create state directories for each module
        os.makedirs(os.path.join(self.temp_dir, "state", "unsupervised_segmentation"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "state", "grain_analysis"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "state", "trainable_segmentation"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "state", "image_enhancement"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "state", "point_counting"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "state", "ai_assistant"), exist_ok=True)

        # Create image_processing directory for backward compatibility
        # This will be removed in a future version
        os.makedirs(os.path.join(self.temp_dir, "state", "image_processing"), exist_ok=True)

        return self.temp_dir

    def add_image(self, source_path: str) -> Optional[ImageInfo]:
        """Add an image to the project.

        Args:
            source_path: Path to the image file to add.

        Returns:
            ImageInfo object for the added image, or None if failed.
        """
        if not os.path.exists(source_path):
            logger.error(f"Image file not found: {source_path}")
            return None

        # Create temp directory if it doesn't exist
        if not self.temp_dir:
            self._create_temp_directory()

        # Generate a unique ID for the image
        image_id = f"img_{len(self.images) + 1}_{os.path.basename(source_path)}"

        # Copy the image to the temp directory
        dest_path = os.path.join(self.temp_dir, "images", os.path.basename(source_path))
        try:
            shutil.copy2(source_path, dest_path)
            logger.info(f"Copied image to project: {dest_path}")
        except Exception as e:
            logger.exception(f"Failed to copy image: {e}")
            return None

        # Create relative path for storage in project file
        rel_path = os.path.join("images", os.path.basename(dest_path))

        # Create and store the image info
        info = ImageInfo(
            id=image_id,
            filename=os.path.basename(dest_path),
            filepath=rel_path,
            metadata={}
        )
        self.images[image_id] = info

        return info

    def get_image_info(self, image_id: str) -> Optional[ImageInfo]:
        """Get information about an image in the project."""
        return self.images.get(image_id)

    def set_metadata(self, image_id: str, metadata: Dict[str, Any]) -> bool:
        """Set metadata for an image in the project.

        Args:
            image_id: ID of the image.
            metadata: Dictionary containing metadata to set.

        Returns:
            True if successful, False otherwise.
        """
        info = self.get_image_info(image_id)
        if not info:
            logger.error(f"Image not found: {image_id}")
            return False

        # Update the metadata
        info.metadata = metadata

        # Mark project as modified
        self.modified_at = datetime.now().isoformat()

        return True

    def get_image_path(self, image_id: str) -> Optional[str]:
        """Get the absolute path to an image in the project."""
        info = self.get_image_info(image_id)
        if not info or not self.temp_dir:
            return None
        return os.path.join(self.temp_dir, info.filepath)

    def remove_image(self, image_id: str) -> bool:
        """Remove an image from the project.

        Args:
            image_id: ID of the image to remove.

        Returns:
            True if successful, False otherwise.
        """
        info = self.get_image_info(image_id)
        if not info:
            logger.error(f"Image not found: {image_id}")
            return False

        # Get the file path
        file_path = self.get_image_path(image_id)

        # Remove from project data structures
        try:
            # Remove from images dictionary
            del self.images[image_id]

            # Clean up all state files for this image
            self.cleanup_image_state(image_id)

            # Mark project as modified
            self.modified_at = datetime.now().isoformat()

            # Delete the file if it exists
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted image file: {file_path}")

            # Check if this image has patches
            patch_ids = []
            for img_id, img_info in list(self.images.items()):
                if img_info.metadata and img_info.metadata.get("is_patch") and \
                   img_info.metadata.get("original_image") == file_path:
                    patch_ids.append(img_id)

            # Remove any patches of this image
            for patch_id in patch_ids:
                self.remove_image(patch_id)

            return True
        except Exception as e:
            logger.exception(f"Failed to remove image {image_id}: {e}")
            return False

    def cleanup_image_state(self, image_id: str) -> None:
        """Clean up all state files associated with an image.

        Args:
            image_id: ID of the image to clean up state for.
        """
        logger.info(f"Cleaning up state files for image: {image_id}")
        print(f"DEBUG: Cleaning up state files for image: {image_id}")

        # Remove from in-memory state dictionaries
        if image_id in self.grain_analysis_states:
            del self.grain_analysis_states[image_id]
            logger.info(f"Removed grain analysis state for {image_id}")
            print(f"DEBUG: Removed grain analysis state for {image_id}")

        if image_id in self.image_processing_states:
            del self.image_processing_states[image_id]
            logger.info(f"Removed unsupervised segmentation state for {image_id}")
            print(f"DEBUG: Removed unsupervised segmentation state for {image_id}")

        if image_id in self.trainable_segmentation_states:
            del self.trainable_segmentation_states[image_id]
            logger.info(f"Removed trainable segmentation state for {image_id}")
            print(f"DEBUG: Removed trainable segmentation state for {image_id}")

        if image_id in self.image_enhancement_states:
            del self.image_enhancement_states[image_id]
            logger.info(f"Removed image enhancement state for {image_id}")
            print(f"DEBUG: Removed image enhancement state for {image_id}")

        if image_id in self.ai_assistant_states:
            del self.ai_assistant_states[image_id]
            logger.info(f"Removed AI Assistant state for {image_id}")
            print(f"DEBUG: Removed AI Assistant state for {image_id}")

        # Remove state files from disk
        if self.temp_dir:
            # Clean up trainable segmentation state files
            ts_state_dir = os.path.join(self.temp_dir, "state", "trainable_segmentation")
            if os.path.exists(ts_state_dir):
                # Remove state JSON file
                state_json = os.path.join(ts_state_dir, f"{image_id}_state.json")
                if os.path.exists(state_json):
                    try:
                        os.remove(state_json)
                        logger.info(f"Deleted trainable segmentation state file: {state_json}")
                        print(f"DEBUG: Deleted trainable segmentation state file: {state_json}")
                    except Exception as e:
                        logger.error(f"Failed to delete state file {state_json}: {e}")
                        print(f"DEBUG: Failed to delete state file {state_json}: {e}")

                # Remove related files (training_labels, result, classifier, mask_colors, etc.)
                for filename in os.listdir(ts_state_dir):
                    if filename.startswith(f"{image_id}_"):
                        try:
                            file_path = os.path.join(ts_state_dir, filename)
                            os.remove(file_path)
                            logger.info(f"Deleted trainable segmentation file: {file_path}")
                            print(f"DEBUG: Deleted trainable segmentation file: {file_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete file {file_path}: {e}")
                            print(f"DEBUG: Failed to delete file {file_path}: {e}")

            # Clean up grain analysis state files
            ga_state_dir = os.path.join(self.temp_dir, "state", "grain_analysis")
            if os.path.exists(ga_state_dir):
                for filename in os.listdir(ga_state_dir):
                    if filename.startswith(f"{image_id}_"):
                        try:
                            file_path = os.path.join(ga_state_dir, filename)
                            os.remove(file_path)
                            logger.info(f"Deleted grain analysis file: {file_path}")
                            print(f"DEBUG: Deleted grain analysis file: {file_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete file {file_path}: {e}")
                            print(f"DEBUG: Failed to delete file {file_path}: {e}")

            # Clean up image processing state files
            ip_state_dir = os.path.join(self.temp_dir, "state", "image_processing")
            if os.path.exists(ip_state_dir):
                for filename in os.listdir(ip_state_dir):
                    if filename.startswith(f"{image_id}_"):
                        try:
                            file_path = os.path.join(ip_state_dir, filename)
                            os.remove(file_path)
                            logger.info(f"Deleted image processing file: {file_path}")
                            print(f"DEBUG: Deleted image processing file: {file_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete file {file_path}: {e}")
                            print(f"DEBUG: Failed to delete file {file_path}: {e}")

            # Clean up image enhancement state files
            ie_state_dir = os.path.join(self.temp_dir, "state", "image_enhancement")
            if os.path.exists(ie_state_dir):
                for filename in os.listdir(ie_state_dir):
                    if filename.startswith(f"{image_id}_"):
                        try:
                            file_path = os.path.join(ie_state_dir, filename)
                            os.remove(file_path)
                            logger.info(f"Deleted image enhancement file: {file_path}")
                            print(f"DEBUG: Deleted image enhancement file: {file_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete file {file_path}: {e}")
                            print(f"DEBUG: Failed to delete file {file_path}: {e}")

            # Clean up AI Assistant state files
            ai_state_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
            if os.path.exists(ai_state_dir):
                for filename in os.listdir(ai_state_dir):
                    if filename.startswith(f"{image_id}_"):
                        try:
                            file_path = os.path.join(ai_state_dir, filename)
                            os.remove(file_path)
                            logger.info(f"Deleted AI Assistant file: {file_path}")
                            print(f"DEBUG: Deleted AI Assistant file: {file_path}")
                        except Exception as e:
                            logger.error(f"Failed to delete file {file_path}: {e}")
                            print(f"DEBUG: Failed to delete file {file_path}: {e}")

    def save_grain_analysis_state(self, image_id: str, state: Dict[str, Any]):
        """Save the grain analysis state for an image.

        Args:
            image_id: ID of the image.
            state: Dictionary containing the state to save.
        """
        self.grain_analysis_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            # Use the dedicated grain_analysis directory in the state folder
            results_dir = os.path.join(self.temp_dir, "state", "grain_analysis", image_id)
            os.makedirs(results_dir, exist_ok=True)

            # Save metadata to JSON
            metadata = {
                'has_results': True,
                'timestamp': datetime.now().isoformat(),
                'scale_factor': state.get('scale_value'),
                'scale_unit': state.get('scale_unit'),
                'num_grains': len(state['df']) if 'df' in state and isinstance(state['df'], pd.DataFrame) else 0
            }

            # Save serializable data
            serializable_state = {}
            for key, value in state.items():
                if key == 'df' and isinstance(value, pd.DataFrame):
                    # Save DataFrame to CSV
                    df_path = os.path.join(results_dir, "dataframe.csv")
                    value.to_csv(df_path, index=False)
                    serializable_state[key] = f"state/grain_analysis/{image_id}/dataframe.csv"
                elif key == 'annotations' and value is not None:
                    # Save annotations to NPZ in the results directory
                    annotations_path = os.path.join(results_dir, "annotations.npz")

                    # Handle different types of annotations
                    if isinstance(value, list):
                        # List of masks
                        logger.info(f"Saving list of annotations with length {len(value)}")
                        if len(value) > 0:
                            logger.info(f"First annotation type: {type(value[0])}")

                        # Convert each mask to a standard format (numpy array) before saving
                        # This ensures consistent restoration
                        standardized_annotations = []
                        for mask in value:
                            if hasattr(mask, 'cpu') and hasattr(mask, 'numpy'):
                                # PyTorch tensor
                                standardized_annotations.append(mask.cpu().numpy())
                            elif hasattr(mask, 'shape') and hasattr(mask, 'astype'):
                                # NumPy array
                                standardized_annotations.append(mask)
                            elif isinstance(mask, dict) and 'segmentation' in mask:
                                # Dictionary with segmentation points
                                standardized_annotations.append(mask)
                            else:
                                # Try to convert to numpy array
                                try:
                                    standardized_annotations.append(np.array(mask))
                                except Exception as e:
                                    logger.warning(f"Could not convert mask to standard format: {e}")
                                    standardized_annotations.append(mask)  # Keep original as fallback

                        np.savez_compressed(annotations_path, annotations=np.array(standardized_annotations, dtype=object), is_list=True)
                        logger.info(f"Saved {len(standardized_annotations)} standardized annotations")
                    elif hasattr(value, 'cpu') and hasattr(value, 'numpy'):
                        # PyTorch tensor
                        np_value = value.cpu().numpy()
                        np.savez_compressed(annotations_path, annotations=np_value, is_tensor=True)
                    else:
                        # Try to save as numpy array
                        np.savez_compressed(annotations_path, annotations=np.array(value, dtype=object))

                    serializable_state[key] = f"state/grain_analysis/{image_id}/annotations.npz"

                    # Update metadata with file size for performance monitoring
                    if os.path.exists(annotations_path):
                        metadata['annotations_file_size'] = os.path.getsize(annotations_path)
                elif key == 'scale_line' and value is not None:
                    # Convert QLineF to serializable format
                    serializable_state[key] = {
                        'x1': value.x1(),
                        'y1': value.y1(),
                        'x2': value.x2(),
                        'y2': value.y2()
                    }
                elif key == 'processed_image_vis' and value is not None:
                    # Skip saving processed_image_vis to disk - it can be recreated from original image + annotations
                    # This saves disk space and I/O since the visualization is redundant with saved annotations
                    logger.debug("Skipping processed_image_vis save - can be recreated from annotations")
                    # Don't include in serializable_state to avoid unnecessary disk usage
                else:
                    # Try to serialize directly
                    try:
                        json.dumps({key: value})
                        serializable_state[key] = value
                    except (TypeError, OverflowError):
                        logger.warning(f"Could not serialize {key} in grain analysis state")

            # Save metadata to JSON
            metadata_path = os.path.join(results_dir, "metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)

            # Save serializable state to JSON
            state_path = os.path.join(results_dir, "state.json")
            with open(state_path, 'w') as f:
                json.dump(serializable_state, f, indent=4)

            # No need to save a reference since we're already in the state directory
            # The state.json file already contains all the necessary information

    def load_grain_analysis_state(self, image_id: str, load_annotations: bool = False) -> Optional[Dict[str, Any]]:
        """Load the grain analysis state for an image.

        Args:
            image_id: ID of the image.
            load_annotations: Whether to load annotations immediately or defer loading.

        Returns:
            Dictionary containing the state, or None if not found.
        """
        # Check if state is already in memory
        if image_id in self.grain_analysis_states:
            state = self.grain_analysis_states[image_id]

            # If annotations are referenced but not loaded, load them if requested
            if load_annotations and 'annotations_path' in state and 'annotations' not in state:
                logger.info(f"Loading annotations on demand for image {image_id}")
                self._load_annotations_for_state(state, state['annotations_path'])
            elif not load_annotations and 'annotations' in state:
                # If we're not requesting annotations but they're already loaded, store the path but remove them
                # This helps with memory usage when switching between images
                if 'annotations_path' not in state:
                    logger.warning(f"Annotations loaded but no path stored for image {image_id}")
                else:
                    logger.info(f"Deferring annotations for image {image_id} to save memory")
                    # Remove the annotations from memory but keep the path
                    del state['annotations']

            return state

        # Try to load state from disk
        if self.temp_dir:
            # Check the state directory first
            results_dir = os.path.join(self.temp_dir, "state", "grain_analysis", image_id)
            metadata_path = os.path.join(results_dir, "metadata.json")
            state_path = os.path.join(results_dir, "state.json")

            # For backward compatibility, also check the old results directory
            old_results_dir = os.path.join(self.temp_dir, "results", "grain_analysis", image_id)
            old_metadata_path = os.path.join(old_results_dir, "metadata.json")
            old_state_path = os.path.join(old_results_dir, "state.json")

            # First check if we have results in the state directory
            if os.path.exists(metadata_path) and os.path.exists(state_path):
                try:
                    # Load metadata for quick access to basic info
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)

                    # Create initial state with metadata
                    state = {
                        'scale_value': metadata.get('scale_factor'),
                        'scale_unit': metadata.get('scale_unit'),
                        'has_results': metadata.get('has_results', False),
                        'timestamp': metadata.get('timestamp'),
                        'num_grains': metadata.get('num_grains', 0)
                    }

                    # Load the full state if needed
                    with open(state_path, 'r') as f:
                        full_state = json.load(f)

                    # Merge with metadata
                    state.update(full_state)

                    # Load DataFrame if needed
                    if 'df' in state and isinstance(state['df'], str):
                        df_path = os.path.join(self.temp_dir, state['df'])
                        if os.path.exists(df_path):
                            # Always load the dataframe for basic display
                            state['df'] = pd.read_csv(df_path)

                    # Handle annotations - store path but don't load yet unless requested
                    if 'annotations' in state and isinstance(state['annotations'], str):
                        annotations_path = os.path.join(self.temp_dir, state['annotations'])
                        if os.path.exists(annotations_path):
                            # Store the path for later loading
                            state['annotations_path'] = annotations_path

                            # Only load annotations if explicitly requested
                            if load_annotations:
                                self._load_annotations_for_state(state, annotations_path)
                            else:
                                # Remove the annotations key to avoid confusion
                                del state['annotations']
                                logger.info(f"Deferred loading annotations for {image_id}")

                    # Skip loading processed_image_vis - it's no longer saved to disk
                    # The visualization can be recreated from original image + annotations when needed
                    if 'processed_image_vis' in state:
                        logger.debug("Removing processed_image_vis from state - will be recreated when needed")
                        del state['processed_image_vis']

                    # Convert scale_line back to QLineF if present
                    if 'scale_line' in state and isinstance(state['scale_line'], dict):
                        from PySide6.QtCore import QLineF
                        line_data = state['scale_line']
                        state['scale_line'] = QLineF(
                            line_data['x1'], line_data['y1'],
                            line_data['x2'], line_data['y2']
                        )

                    # Store in memory
                    self.grain_analysis_states[image_id] = state
                    return state
                except Exception as e:
                    logger.exception(f"Failed to load grain analysis state from state directory: {e}")

                    # Try the old results directory for backward compatibility
                    if os.path.exists(old_metadata_path) and os.path.exists(old_state_path):
                        try:
                            # Load metadata and state from the old results directory
                            with open(old_metadata_path, 'r') as f:
                                metadata = json.load(f)

                            with open(old_state_path, 'r') as f:
                                state = json.load(f)

                            # Process the state as before
                            # Load DataFrame if referenced
                            if 'df' in state and isinstance(state['df'], str):
                                df_path = os.path.join(self.temp_dir, state['df'])
                                if os.path.exists(df_path):
                                    state['df'] = pd.read_csv(df_path)

                            # Handle annotations reference
                            if 'annotations' in state and isinstance(state['annotations'], str):
                                annotations_path = os.path.join(self.temp_dir, state['annotations'])
                                if os.path.exists(annotations_path):
                                    # Store the path for later loading
                                    state['annotations_path'] = annotations_path

                                    # Only load annotations if explicitly requested
                                    if load_annotations:
                                        self._load_annotations_for_state(state, annotations_path)
                                    else:
                                        # Remove the annotations key to avoid confusion
                                        del state['annotations']
                                        logger.info(f"Deferred loading annotations for {image_id}")

                            # Store in memory
                            self.grain_analysis_states[image_id] = state

                            # Copy files to the new location for future use
                            try:
                                # Create the new directory if it doesn't exist
                                if not os.path.exists(results_dir):
                                    os.makedirs(results_dir, exist_ok=True)

                                # Copy metadata and state files
                                shutil.copy2(old_metadata_path, metadata_path)
                                shutil.copy2(old_state_path, state_path)

                                # Copy other files if they exist (excluding processed_vis.png since we no longer save it)
                                for filename in ['dataframe.csv', 'annotations.npz']:
                                    old_file = os.path.join(old_results_dir, filename)
                                    new_file = os.path.join(results_dir, filename)
                                    if os.path.exists(old_file):
                                        shutil.copy2(old_file, new_file)

                                logger.info(f"Copied grain analysis files from old results directory to state directory for {image_id}")
                            except Exception as e:
                                logger.warning(f"Failed to copy files to new location: {e}")

                            return state
                        except Exception as e2:
                            logger.exception(f"Failed to load grain analysis state from old results directory: {e2}")

            # Fall back to the old format if new format not found or failed
            state_dir = os.path.join(self.temp_dir, "state", "grain_analysis")
            old_state_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(old_state_path):
                try:
                    with open(old_state_path, 'r') as f:
                        state = json.load(f)

                    # Check if this is a reference to the new format
                    if 'results_path' in state:
                        # This is just a reference, try loading from the new path
                        results_path = os.path.join(self.temp_dir, state['results_path'])
                        if os.path.exists(results_path):
                            # Recursively call this method to load from the new path
                            return self.load_grain_analysis_state(image_id, load_annotations)

                    # Otherwise, this is the old format - load it directly
                    # Load DataFrame if referenced
                    if 'df' in state and isinstance(state['df'], str):
                        df_path = os.path.join(self.temp_dir, state['df'])
                        if os.path.exists(df_path):
                            state['df'] = pd.read_csv(df_path)

                    # Handle annotations reference - store path but don't load yet unless requested
                    if 'annotations' in state and isinstance(state['annotations'], str):
                        annotations_path = os.path.join(self.temp_dir, state['annotations'])
                        if os.path.exists(annotations_path):
                            # Store the path for later loading
                            state['annotations_path'] = annotations_path

                            # Only load annotations if explicitly requested
                            if load_annotations:
                                self._load_annotations_for_state(state, annotations_path)
                            else:
                                # Remove the annotations key to avoid confusion
                                del state['annotations']
                                logger.info(f"Deferred loading annotations for {image_id}")

                    # Skip loading processed_image_vis - it's no longer saved to disk
                    # The visualization will be recreated from original image + annotations when needed
                    if 'processed_image_vis' in state:
                        logger.debug("Removing processed_image_vis from state - will be recreated when needed")
                        del state['processed_image_vis']

                    # Convert scale_line back to QLineF
                    if 'scale_line' in state and isinstance(state['scale_line'], dict):
                        from PySide6.QtCore import QLineF
                        line_data = state['scale_line']
                        state['scale_line'] = QLineF(
                            line_data['x1'], line_data['y1'],
                            line_data['x2'], line_data['y2']
                        )

                    # Store in memory
                    self.grain_analysis_states[image_id] = state
                    return state
                except Exception as e:
                    logger.exception(f"Failed to load grain analysis state from old format: {e}")

        return None

    def _load_annotations_for_state(self, state: Dict[str, Any], annotations_path: str) -> None:
        """Load annotations from file into the state dictionary.

        Args:
            state: The state dictionary to update with loaded annotations.
            annotations_path: Path to the annotations file.
        """
        try:
            # Check if file exists before attempting to load
            if not os.path.exists(annotations_path):
                logger.error(f"Annotations file not found: {annotations_path}")
                return

            # Measure loading time for performance monitoring
            start_time = time.time()

            with np.load(annotations_path, allow_pickle=True) as data:
                logger.info(f"Loading annotations from {annotations_path}")
                logger.debug(f"Annotation data keys: {list(data.keys())}")

                if 'is_list' in data and data['is_list']:
                    # Convert back to list
                    annotations_list = data['annotations'].tolist()
                    logger.info(f"Loaded annotations as list, length: {len(annotations_list)}")

                    # Ensure each annotation is in the correct format for drawing
                    # This is critical for interactive polygons to work
                    processed_annotations = []
                    for i, mask in enumerate(annotations_list):
                        if isinstance(mask, dict) and 'segmentation' in mask:
                            # Dictionary with segmentation points - keep as is
                            processed_annotations.append(mask)
                        elif hasattr(mask, 'shape'):
                            # NumPy array - convert to tensor for consistency
                            try:
                                import torch
                                processed_annotations.append(torch.from_numpy(mask))
                            except Exception as e:
                                logger.warning(f"Failed to convert numpy to tensor: {e}, keeping original")
                                processed_annotations.append(mask)
                        else:
                            # Unknown format - try to handle it
                            logger.warning(f"Unknown annotation format for item {i}: {type(mask)}")
                            processed_annotations.append(mask)

                    state['annotations'] = processed_annotations
                    logger.info(f"Processed {len(processed_annotations)} annotations for interactive display")
                elif 'is_tensor' in data and data['is_tensor']:
                    # Convert back to tensor
                    import torch
                    state['annotations'] = torch.from_numpy(data['annotations'])
                    logger.info(f"Loaded annotations as tensor, shape: {state['annotations'].shape}")
                else:
                    # Regular numpy array
                    state['annotations'] = data['annotations']
                    logger.info(f"Loaded annotations as numpy array, shape: {state['annotations'].shape if hasattr(state['annotations'], 'shape') else 'unknown'}")

                # Store the path for future reference (useful for reloading)
                state['annotations_path'] = annotations_path

                # Log performance metrics
                elapsed_time = time.time() - start_time
                logger.info(f"Annotations loaded in {elapsed_time:.2f} seconds")

        except Exception as e:
            logger.exception(f"Failed to load annotations: {e}")
            # Make sure we don't have a partial state
            if 'annotations' in state:
                del state['annotations']
            # Keep the path for potential retry
            state['annotations_path'] = annotations_path

    def save_image_processing_state(self, image_id: str, state: Dict[str, Any]):
        """Save the image processing state for an image.

        Simplified to only save the segmented image and essential settings.
        """
        # Only save if we have a segmented image
        if 'segmented_image' not in state or state['segmented_image'] is None:
            logger.info(f"No segmented image to save for {image_id}")
            return

        # Store in memory
        self.image_processing_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            # Use the new directory name for unsupervised segmentation
            state_dir = os.path.join(self.temp_dir, "state", "unsupervised_segmentation")
            os.makedirs(state_dir, exist_ok=True)

            # Create the old directory for backward compatibility
            old_state_dir = os.path.join(self.temp_dir, "state", "image_processing")
            os.makedirs(old_state_dir, exist_ok=True)

            # Save serializable data to JSON
            serializable_state = {}

            # Save segmented image to NPZ
            if 'segmented_image' in state and state['segmented_image'] is not None:
                # Save to new directory
                seg_path = os.path.join(state_dir, f"{image_id}_segmented.npz")
                np.savez_compressed(seg_path, segmented_image=state['segmented_image'])
                serializable_state['segmented_image'] = f"state/unsupervised_segmentation/{image_id}_segmented.npz"

                # Also save to old directory for backward compatibility
                old_seg_path = os.path.join(old_state_dir, f"{image_id}_segmented.npz")
                np.savez_compressed(old_seg_path, segmented_image=state['segmented_image'])

            # Handle color palette
            if 'new_colors' in state and state['new_colors']:
                # Convert tuple keys to strings for JSON serialization
                serializable_colors = {}
                for color_key, color_value in state['new_colors'].items():
                    # Convert tuple to string representation
                    str_key = str(color_key)
                    # Convert tuple values to lists for JSON serialization
                    if isinstance(color_value, tuple):
                        color_value = list(color_value)
                    serializable_colors[str_key] = color_value
                serializable_state['new_colors'] = serializable_colors

            # Handle segment_names specially to convert tuple keys to strings
            if 'segment_names' in state and state['segment_names'] is not None:
                try:
                    # Convert tuple keys to strings for JSON serialization
                    serializable_names = {}
                    for color_key, name_value in state['segment_names'].items():
                        # Convert tuple to string representation
                        str_key = str(color_key)
                        serializable_names[str_key] = name_value
                    serializable_state['segment_names'] = serializable_names
                except Exception as e:
                    logger.warning(f"Could not serialize segment_names: {e}")

            # Add timestamp
            if 'timestamp' in state and state['timestamp'] is not None:
                try:
                    json.dumps({'timestamp': state['timestamp']})
                    serializable_state['timestamp'] = state['timestamp']
                except (TypeError, OverflowError):
                    logger.warning(f"Could not serialize timestamp in image processing state")

            # Handle label_percentages specially to convert uint8 to int
            if 'label_percentages' in state and state['label_percentages'] is not None:
                try:
                    # Convert uint8 values to regular integers
                    serializable_percentages = {}
                    for label, percentage in state['label_percentages'].items():
                        # Convert label to string if it's not already
                        label_key = str(label) if not isinstance(label, str) else label
                        # Convert percentage to regular int/float if it's numpy type
                        if hasattr(percentage, 'item'):
                            percentage = percentage.item()
                        serializable_percentages[label_key] = percentage
                    serializable_state['label_percentages'] = serializable_percentages
                except Exception as e:
                    logger.warning(f"Could not serialize label_percentages: {e}")

            # Save serializable state to JSON
            # New directory
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            temp_path = os.path.join(state_dir, f"{image_id}_state.json.tmp")

            # Old directory
            old_state_path = os.path.join(old_state_dir, f"{image_id}_state.json")
            old_temp_path = os.path.join(old_state_dir, f"{image_id}_state.json.tmp")
            try:
                # Save to new directory
                with open(temp_path, 'w') as f:
                    json.dump(serializable_state, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())

                # Now rename the temporary file to the final name
                # This is an atomic operation that prevents corruption
                if os.path.exists(state_path):
                    os.remove(state_path)
                os.rename(temp_path, state_path)

                # Also save to old directory for backward compatibility
                with open(old_temp_path, 'w') as f:
                    json.dump(serializable_state, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())

                if os.path.exists(old_state_path):
                    os.remove(old_state_path)
                os.rename(old_temp_path, old_state_path)

                logger.info(f"Successfully saved state for {image_id} to both directories")
            except Exception as e:
                logger.error(f"Error saving state for {image_id}: {e}")
                # Clean up temporary files if they exist
                for path in [temp_path, old_temp_path]:
                    if os.path.exists(path):
                        try:
                            os.remove(path)
                        except:
                            pass

    def save_unsupervised_segmentation_state(self, image_id: str, state: Dict[str, Any]):
        """Save the unsupervised segmentation state for an image.

        This is an alias for save_image_processing_state to maintain compatibility
        with the segmentation handlers.

        Args:
            image_id: ID of the image.
            state: Dictionary containing the state to save.
        """
        return self.save_image_processing_state(image_id, state)

    def save_ai_assistant_state(self, image_id: str, state: Dict[str, Any]):
        """Save the AI Assistant state for an image.

        Args:
            image_id: ID of the image.
            state: Dictionary containing the state to save.
        """
        # Store in memory
        self.ai_assistant_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            # Create the ai_assistant directory in the state folder
            state_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
            os.makedirs(state_dir, exist_ok=True)

            # Save the state JSON
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            try:
                with open(state_path, 'w') as f:
                    json.dump(state, f, indent=2)
                logger.info(f"Saved AI Assistant state for {image_id}")
            except Exception as e:
                logger.error(f"Error saving AI Assistant state: {e}")

    def load_ai_assistant_state(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the AI Assistant state for an image.

        Args:
            image_id: ID of the image.

        Returns:
            Dictionary containing the state, or None if not found.
        """
        # Check if state is already in memory
        if image_id in self.ai_assistant_states:
            return self.ai_assistant_states[image_id]

        # Try to load state from disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(state_path):
                try:
                    # Load the state JSON
                    with open(state_path, 'r') as f:
                        state = json.load(f)

                    # Store in memory
                    self.ai_assistant_states[image_id] = state
                    logger.info(f"Loaded AI Assistant state for {image_id}")
                    return state
                except Exception as e:
                    logger.exception(f"Failed to load AI Assistant state: {e}")

        return None

    def has_ai_assistant_state(self, image_id: str) -> bool:
        """Check if AI Assistant state exists for an image without loading it.

        Args:
            image_id: ID of the image.

        Returns:
            True if state exists, False otherwise.
        """
        # Check if state is already in memory
        if image_id in self.ai_assistant_states:
            return True

        # Check if state exists on disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            return os.path.exists(state_path)

        return False

    def save_ai_analysis_results(self, image_id: str, results: Dict[str, Any]):
        """Save the AI analysis results for an image.

        Args:
            image_id: ID of the image.
            results: Dictionary containing the analysis results.
        """
        if not self.temp_dir:
            logger.error("Cannot save AI analysis results: No project directory")
            return

        # Create the ai_assistant directory in the state folder
        results_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
        os.makedirs(results_dir, exist_ok=True)

        # Save the results JSON
        results_path = os.path.join(results_dir, f"{image_id}_results.json")
        try:
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Saved AI analysis results for {image_id}")
        except Exception as e:
            logger.error(f"Error saving AI analysis results: {e}")

    def load_ai_analysis_results(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the AI analysis results for an image.

        Args:
            image_id: ID of the image.

        Returns:
            Dictionary containing the analysis results, or None if not found.
        """
        if not self.temp_dir:
            logger.error("Cannot load AI analysis results: No project directory")
            return None

        # Get the results file path
        results_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
        results_path = os.path.join(results_dir, f"{image_id}_results.json")

        if not os.path.exists(results_path):
            logger.info(f"No AI analysis results found for {image_id}")
            return None

        try:
            with open(results_path, 'r') as f:
                results = json.load(f)
            logger.info(f"Loaded AI analysis results for {image_id}")
            return results
        except Exception as e:
            logger.error(f"Error loading AI analysis results: {e}")
            return None

    def has_ai_analysis_results(self, image_id: str) -> bool:
        """Check if AI analysis results exist for an image.

        Args:
            image_id: ID of the image.

        Returns:
            True if results exist, False otherwise.
        """
        if not self.temp_dir:
            return False

        results_dir = os.path.join(self.temp_dir, "state", "ai_assistant")
        results_path = os.path.join(results_dir, f"{image_id}_results.json")
        return os.path.exists(results_path)

    def has_unsupervised_segmentation_state(self, image_id: str) -> bool:
        """Check if unsupervised segmentation state exists for an image without loading it.

        Args:
            image_id: ID of the image.

        Returns:
            True if state exists, False otherwise.
        """
        # Check if state is already in memory
        if image_id in self.image_processing_states:
            return True

        # Check if state exists on disk
        if self.temp_dir:
            # First check the new directory name
            state_dir = os.path.join(self.temp_dir, "state", "unsupervised_segmentation")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            if os.path.exists(state_path):
                return True

            # If not found, check the old directory name for backward compatibility
            state_dir = os.path.join(self.temp_dir, "state", "image_processing")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            return os.path.exists(state_path)

        return False

    def has_image_processing_state(self, image_id: str) -> bool:
        """Alias for has_unsupervised_segmentation_state for backward compatibility.

        Args:
            image_id: ID of the image.

        Returns:
            True if state exists, False otherwise.
        """
        return self.has_unsupervised_segmentation_state(image_id)

    def load_image_processing_state(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the image processing state for an image.

        Simplified to only load the segmented image and essential settings.
        """
        # Check if state is already in memory
        if image_id in self.image_processing_states:
            return self.image_processing_states[image_id]

        # Try to load state from disk
        if self.temp_dir:
            # First try the new directory name
            state_dir = os.path.join(self.temp_dir, "state", "unsupervised_segmentation")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")

            # If not found, try the old directory name for backward compatibility
            if not os.path.exists(state_path):
                state_dir = os.path.join(self.temp_dir, "state", "image_processing")
                state_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(state_path):
                try:
                    # Load the state JSON
                    with open(state_path, 'r') as f:
                        state = json.load(f)

                    # Load segmented image if referenced
                    if 'segmented_image' in state and isinstance(state['segmented_image'], str):
                        seg_path = os.path.join(self.temp_dir, state['segmented_image'])
                        if os.path.exists(seg_path):
                            try:
                                with np.load(seg_path) as data:
                                    state['segmented_image'] = data['segmented_image']
                                    logger.info(f"Loaded segmented image for {image_id}")
                            except Exception as e:
                                logger.exception(f"Failed to load segmented image: {e}")
                                state['segmented_image'] = None
                                return None  # If we can't load the segmented image, return None
                        else:
                            logger.warning(f"Segmented image file not found: {seg_path}")
                            return None  # If the segmented image file doesn't exist, return None

                    # Convert serialized new_colors back to proper format
                    if 'new_colors' in state and isinstance(state['new_colors'], dict):
                        try:
                            # Convert string keys back to tuples
                            new_colors = {}
                            for str_key, color_value in state['new_colors'].items():
                                # Convert string representation back to tuple
                                if str_key.startswith('(') and str_key.endswith(')'):
                                    # Parse the string representation of a tuple
                                    key_parts = str_key.strip('()').split(',')
                                    key_tuple = tuple(int(part.strip()) for part in key_parts)
                                    # Convert list values back to tuples if needed
                                    if isinstance(color_value, list):
                                        color_value = tuple(color_value)
                                    new_colors[key_tuple] = color_value
                                else:
                                    # Keep as is if not a tuple representation
                                    new_colors[str_key] = color_value
                            state['new_colors'] = new_colors
                            logger.info(f"Restored {len(new_colors)} color mappings")
                        except Exception as e:
                            logger.exception(f"Failed to restore new_colors: {e}")
                            state['new_colors'] = {}

                    # Initialize segmented_images as empty list (we don't save this anymore)
                    state['segmented_images'] = []

                    # Convert label_percentages keys back to integers if they're strings
                    if 'label_percentages' in state and isinstance(state['label_percentages'], dict):
                        try:
                            converted_percentages = {}
                            for label_key, percentage in state['label_percentages'].items():
                                # Try to convert string keys back to integers
                                if isinstance(label_key, str) and label_key.isdigit():
                                    label_key = int(label_key)
                                converted_percentages[label_key] = percentage
                            state['label_percentages'] = converted_percentages
                        except Exception as e:
                            logger.warning(f"Failed to convert label_percentages keys: {e}")

                    # Convert segment_names keys back to tuples if they're strings
                    if 'segment_names' in state and isinstance(state['segment_names'], dict):
                        try:
                            # Convert string keys back to tuples
                            converted_names = {}
                            for str_key, name_value in state['segment_names'].items():
                                # Try to convert string representation back to tuple
                                if str_key.startswith('(') and str_key.endswith(')'):
                                    # Parse the string representation of a tuple
                                    key_parts = str_key.strip('()').split(',')
                                    key_tuple = tuple(int(part.strip()) for part in key_parts)
                                    converted_names[key_tuple] = name_value
                                else:
                                    # Keep as is if not a tuple representation
                                    converted_names[str_key] = name_value
                            state['segment_names'] = converted_names
                            logger.info(f"Restored {len(converted_names)} segment names")
                        except Exception as e:
                            logger.warning(f"Failed to convert segment_names keys: {e}")

                    # Store in memory
                    self.image_processing_states[image_id] = state
                    return state
                except json.JSONDecodeError as e:
                    logger.exception(f"JSON decode error in {state_path}: {e}")
                except Exception as e:
                    logger.exception(f"Failed to load image processing state: {e}")

        return None

    def save_trainable_segmentation_state(self, image_id: str, state: Dict[str, Any]):
        """Save the trainable segmentation state for an image."""
        # Print debug information
        print(f"DEBUG: GrainSightProject.save_trainable_segmentation_state called for {image_id}")
        print(f"DEBUG: State keys: {list(state.keys())}")
        if 'training_labels' in state:
            print(f"DEBUG: training_labels type: {type(state['training_labels'])}")
            if state['training_labels'] is not None:
                print(f"DEBUG: training_labels shape: {state['training_labels'].shape}")
                print(f"DEBUG: training_labels unique values: {np.unique(state['training_labels'])}")

        # Store in memory
        self.trainable_segmentation_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            # Create main state directory
            state_dir = os.path.join(self.temp_dir, "state", "trainable_segmentation")
            os.makedirs(state_dir, exist_ok=True)
            print(f"DEBUG: Saving to directory: {state_dir}")

            # Create a directory for this image's annotations
            image_dir_name = image_id.replace('.', '_')
            image_dir = os.path.join(state_dir, image_dir_name)
            os.makedirs(image_dir, exist_ok=True)
            print(f"DEBUG: Created image directory: {image_dir}")

            # Save serializable data to JSON
            serializable_state = {}

            # Process each key in the state dictionary
            for key, value in state.items():
                print(f"DEBUG: Processing key: {key}, type: {type(value) if value is not None else 'None'}")

                # Skip features - they can be recalculated when needed
                if key == 'features':
                    print(f"DEBUG: Skipping 'features' to save disk space - will be recalculated when needed")
                    serializable_state[key] = None
                    continue

                # Handle NumPy arrays
                if value is not None and isinstance(value, np.ndarray):
                    # Save array to NPZ in the image directory
                    array_filename = f"{key}.npz"
                    array_path = os.path.join(image_dir, array_filename)
                    try:
                        print(f"DEBUG: Saving NumPy array '{key}' to {array_path}")
                        # Create a dictionary with the key as the key and the array as the value
                        array_dict = {key: value}
                        np.savez_compressed(array_path, **array_dict)
                        serializable_state[key] = f"state/trainable_segmentation/{image_dir_name}/{array_filename}"
                        print(f"DEBUG: Successfully saved NumPy array '{key}' to {array_path}")
                    except Exception as e:
                        print(f"DEBUG: Failed to save NumPy array '{key}': {e}")
                        logger.error(f"Failed to save NumPy array '{key}': {e}")

                # Handle classifier (special case for pickle)
                elif key == 'classifier' and value is not None:
                    # Save classifier to pickle in the image directory
                    classifier_filename = "classifier.pkl"
                    classifier_path = os.path.join(image_dir, classifier_filename)
                    try:
                        print(f"DEBUG: Saving classifier to {classifier_path}")
                        with open(classifier_path, 'wb') as f:
                            pickle.dump(value, f)
                        serializable_state[key] = f"state/trainable_segmentation/{image_dir_name}/{classifier_filename}"
                        print(f"DEBUG: Successfully saved classifier to {classifier_path}")
                    except Exception as e:
                        print(f"DEBUG: Failed to save classifier: {e}")
                        logger.error(f"Failed to save classifier: {e}")

                # Handle dictionaries
                elif key in ['label_names', 'mask_colors', 'feature_params'] and value is not None:
                    # These are dictionaries that should be serializable
                    print(f"DEBUG: Adding dictionary '{key}' directly to serializable state")
                    serializable_state[key] = value

                # Handle simple values
                elif key in ['current_label', 'brush_size', 'mask_overlay_alpha', 'max_label_index', 'timestamp']:
                    # These are simple values that should be serializable
                    print(f"DEBUG: Adding simple value '{key}' directly to serializable state")
                    serializable_state[key] = value

                # Try to serialize directly for other types
                else:
                    # Try to serialize directly
                    try:
                        print(f"DEBUG: Trying to serialize '{key}' directly")
                        json.dumps({key: value})
                        serializable_state[key] = value
                        print(f"DEBUG: Successfully serialized '{key}' directly")
                    except (TypeError, OverflowError) as e:
                        print(f"DEBUG: Could not serialize '{key}' in trainable segmentation state: {e}")
                        logger.warning(f"Could not serialize '{key}' in trainable segmentation state: {e}")

            # Save serializable state to JSON in the image directory
            state_path = os.path.join(image_dir, "state.json")
            print(f"DEBUG: Saving serializable state to {state_path}")
            print(f"DEBUG: Serializable state keys: {list(serializable_state.keys())}")

            # First write to a temporary file to avoid corruption
            temp_path = os.path.join(image_dir, "state.json.tmp")
            try:
                with open(temp_path, 'w') as f:
                    print(f"DEBUG: Writing serializable state to temporary file: {temp_path}")
                    json.dump(serializable_state, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())
                    print(f"DEBUG: Successfully wrote serializable state to temporary file")

                # Now rename the temporary file to the final name
                # This is an atomic operation that prevents corruption
                if os.path.exists(state_path):
                    print(f"DEBUG: Removing existing state file: {state_path}")
                    os.remove(state_path)
                print(f"DEBUG: Renaming temporary file to final name")
                os.rename(temp_path, state_path)
                print(f"DEBUG: Successfully renamed temporary file to final name")

                # Create a pointer file in the main state directory
                pointer_path = os.path.join(state_dir, f"{image_id}_state.json")
                pointer_temp_path = os.path.join(state_dir, f"{image_id}_state.json.tmp")
                with open(pointer_temp_path, 'w') as f:
                    pointer_data = {
                        "state_path": f"state/trainable_segmentation/{image_dir_name}/state.json",
                        "timestamp": state.get('timestamp', datetime.now().isoformat())
                    }
                    json.dump(pointer_data, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())

                # Rename pointer file
                if os.path.exists(pointer_path):
                    os.remove(pointer_path)
                os.rename(pointer_temp_path, pointer_path)
                print(f"DEBUG: Created pointer file at {pointer_path}")

                logger.info(f"Successfully saved trainable segmentation state for {image_id}")
                print(f"DEBUG: Successfully saved trainable segmentation state for {image_id}")
            except Exception as e:
                logger.error(f"Error saving trainable segmentation state for {image_id}: {e}")
                print(f"DEBUG: Error saving trainable segmentation state for {image_id}: {e}")
                import traceback
                traceback.print_exc()
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except Exception as e:
                        print(f"DEBUG: Error removing temporary file: {e}")

    def has_trainable_segmentation_state(self, image_id: str) -> bool:
        """Check if trainable segmentation state exists for an image without loading it.

        Args:
            image_id: ID of the image.

        Returns:
            True if state exists, False otherwise.
        """
        print(f"DEBUG: Checking if trainable segmentation state exists for {image_id}")

        # Check if state is already in memory
        if image_id in self.trainable_segmentation_states:
            print(f"DEBUG: State found in memory for {image_id}")
            return True

        # Check if state exists on disk
        if self.temp_dir:
            # Check for pointer file (new format)
            state_dir = os.path.join(self.temp_dir, "state", "trainable_segmentation")
            pointer_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(pointer_path):
                print(f"DEBUG: Pointer file found at {pointer_path}")
                try:
                    # Load the pointer file
                    with open(pointer_path, 'r') as f:
                        pointer_data = json.load(f)

                    # Check if the state file exists
                    if 'state_path' in pointer_data:
                        state_path = os.path.join(self.temp_dir, pointer_data['state_path'])
                        if os.path.exists(state_path):
                            print(f"DEBUG: State file found at {state_path}")
                            return True
                except Exception as e:
                    print(f"DEBUG: Error checking pointer file: {e}")

            # Check for old format state file
            old_state_path = os.path.join(state_dir, f"{image_id}_state.json")
            if os.path.exists(old_state_path):
                print(f"DEBUG: Old format state file found at {old_state_path}")
                return True

        return False

    def has_point_counting_state(self, image_id: str) -> bool:
        """Check if point counting state exists for an image without loading it.

        Args:
            image_id: ID of the image.

        Returns:
            True if state exists, False otherwise.
        """
        # Check if state is already in memory
        if image_id in self.point_counting_states:
            return True

        # Check if state exists on disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "point_counting")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            return os.path.exists(state_path)

        return False

    def save_point_counting_state(self, image_id: str, state: Dict[str, Any]):
        """Save the point counting state for an image.

        Args:
            image_id: ID of the image.
            state: The state dictionary to save.
        """
        # Store in memory
        self.point_counting_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "point_counting")
            os.makedirs(state_dir, exist_ok=True)

            # Create a serializable copy of the state
            serializable_state = {}

            # Process each key in the state dictionary
            for key, value in state.items():
                # Handle points list - preserve all point data including classification method
                if key == 'points' and isinstance(value, list):
                    serializable_points = []
                    for point in value:
                        if len(point) >= 4:  # (x, y, class_idx, classification_method)
                            x, y, class_idx, classification_method = point
                            serializable_points.append((float(x), float(y), int(class_idx), int(classification_method)))
                        elif len(point) == 3:  # (x, y, class_idx) - older format
                            x, y, class_idx = point
                            # Default to manual classification (0) for backward compatibility
                            serializable_points.append((float(x), float(y), int(class_idx), 0))
                    serializable_state[key] = serializable_points

                # Handle classes list - convert QColor objects to RGB strings
                elif key == 'classes' and isinstance(value, list):
                    serializable_classes = []
                    for class_name, color in value:
                        if hasattr(color, 'name'):  # QColor object
                            serializable_classes.append((class_name, color.name()))
                        else:  # Already serialized
                            serializable_classes.append((class_name, color))
                    serializable_state[key] = serializable_classes

                # Handle simple values
                elif key in ['current_class_index', 'counting_method', 'grid_size', 'random_count']:
                    serializable_state[key] = value

                # Try to serialize directly for other types
                else:
                    try:
                        json.dumps({key: value})
                        serializable_state[key] = value
                    except (TypeError, OverflowError):
                        pass  # Skip non-serializable values

            # Save serializable state to JSON
            state_path = os.path.join(state_dir, f"{image_id}_state.json")

            # First write to a temporary file to avoid corruption
            temp_path = os.path.join(state_dir, f"{image_id}_state.json.tmp")
            try:
                with open(temp_path, 'w') as f:
                    json.dump(serializable_state, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())

                # Now rename the temporary file to the final name
                if os.path.exists(state_path):
                    os.remove(state_path)
                os.rename(temp_path, state_path)

                print(f"Successfully saved point counting state for {image_id}")
            except Exception as e:
                print(f"Error saving point counting state for {image_id}: {e}")
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass

    def load_point_counting_state(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the point counting state for an image.

        Args:
            image_id: ID of the image.

        Returns:
            The state dictionary if found, None otherwise.
        """
        # Check if state is already in memory
        if image_id in self.point_counting_states:
            return self.point_counting_states[image_id]

        # Try to load state from disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "point_counting")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(state_path):
                try:
                    # Load the state JSON
                    with open(state_path, 'r') as f:
                        state = json.load(f)

                    # Convert serialized classes back to QColor objects
                    if 'classes' in state and isinstance(state['classes'], list):
                        from PySide6.QtGui import QColor
                        classes = []
                        for class_name, color_str in state['classes']:
                            classes.append((class_name, QColor(color_str)))
                        state['classes'] = classes

                    # Store in memory
                    self.point_counting_states[image_id] = state
                    return state
                except Exception as e:
                    print(f"Failed to load point counting state: {e}")

        return None

    def load_trainable_segmentation_state(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the trainable segmentation state for an image."""
        print(f"DEBUG: GrainSightProject.load_trainable_segmentation_state called for {image_id}")

        # Check if state is already in memory
        if image_id in self.trainable_segmentation_states:
            print(f"DEBUG: State found in memory for {image_id}")
            return self.trainable_segmentation_states[image_id]

        # Try to load state from disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "trainable_segmentation")
            pointer_path = os.path.join(state_dir, f"{image_id}_state.json")
            print(f"DEBUG: Looking for pointer file at {pointer_path}")

            # First check if we have a pointer file (new format)
            if os.path.exists(pointer_path):
                print(f"DEBUG: Pointer file found at {pointer_path}")
                try:
                    # Load the pointer file
                    with open(pointer_path, 'r') as f:
                        pointer_data = json.load(f)

                    # Get the actual state path from the pointer
                    if 'state_path' in pointer_data:
                        state_path = os.path.join(self.temp_dir, pointer_data['state_path'])
                        print(f"DEBUG: State path from pointer: {state_path}")

                        # Check if the state file exists
                        if os.path.exists(state_path):
                            # Load the state file
                            with open(state_path, 'r') as f:
                                state = json.load(f)
                            print(f"DEBUG: Successfully loaded state from {state_path}")
                            print(f"DEBUG: State keys: {list(state.keys())}")

                            logger.info(f"Loading trainable segmentation state for {image_id} from {state_path}")

                            # Process each key in the state dictionary
                            for key, value in list(state.items()):
                                # Check if the value is a string reference to a file
                                if isinstance(value, str) and value.startswith('state/trainable_segmentation/'):
                                    file_path = os.path.join(self.temp_dir, value)
                                    print(f"DEBUG: Found file reference for {key}: {file_path}")

                                    # Handle NPZ files (NumPy arrays)
                                    if file_path.endswith('.npz') and os.path.exists(file_path):
                                        try:
                                            print(f"DEBUG: Loading NumPy array from {file_path}")
                                            with np.load(file_path, allow_pickle=True) as data:
                                                # The key in the NPZ file should match the key in the state dictionary
                                                if key in data:
                                                    state[key] = data[key]
                                                    print(f"DEBUG: Successfully loaded NumPy array for {key} with shape {state[key].shape}")
                                                else:
                                                    # Try to get the first array in the file
                                                    array_keys = list(data.keys())
                                                    if array_keys:
                                                        state[key] = data[array_keys[0]]
                                                        print(f"DEBUG: Loaded NumPy array for {key} using key {array_keys[0]} with shape {state[key].shape}")
                                                    else:
                                                        print(f"DEBUG: No arrays found in {file_path}")
                                                        state[key] = None
                                            logger.info(f"Loaded NumPy array from {file_path}")
                                        except Exception as e:
                                            logger.error(f"Failed to load NumPy array for {key}: {e}")
                                            print(f"DEBUG: Failed to load NumPy array for {key}: {e}")
                                            state[key] = None

                                    # Handle pickle files
                                    elif file_path.endswith('.pkl') and os.path.exists(file_path):
                                        try:
                                            print(f"DEBUG: Loading pickle from {file_path}")
                                            with open(file_path, 'rb') as f:
                                                state[key] = pickle.load(f)
                                            print(f"DEBUG: Successfully loaded pickle for {key}")
                                            logger.info(f"Loaded pickle from {file_path}")
                                        except Exception as e:
                                            logger.error(f"Failed to load pickle for {key}: {e}")
                                            print(f"DEBUG: Failed to load pickle for {key}: {e}")
                                            state[key] = None

                                    # Handle missing files
                                    elif not os.path.exists(file_path):
                                        logger.warning(f"File not found: {file_path}")
                                        print(f"DEBUG: File not found: {file_path}")
                                        state[key] = None

                            # Store in memory
                            self.trainable_segmentation_states[image_id] = state
                            logger.info(f"Successfully loaded trainable segmentation state for {image_id}")
                            return state
                        else:
                            logger.warning(f"State file not found: {state_path}")
                            print(f"DEBUG: State file not found: {state_path}")
                    else:
                        logger.warning(f"No state_path in pointer data: {pointer_data}")
                        print(f"DEBUG: No state_path in pointer data: {pointer_data}")
                except Exception as e:
                    logger.exception(f"Failed to load trainable segmentation state from pointer: {e}")
                    print(f"DEBUG: Failed to load trainable segmentation state from pointer: {e}")

            # If we couldn't load from the pointer or there is no pointer, try the old format
            old_state_path = os.path.join(state_dir, f"{image_id}_state.json")
            print(f"DEBUG: Looking for old format state file at {old_state_path}")

            if os.path.exists(old_state_path):
                print(f"DEBUG: Old format state file found at {old_state_path}")
                try:
                    with open(old_state_path, 'r') as f:
                        state = json.load(f)
                    print(f"DEBUG: Successfully loaded state from {old_state_path}")
                    print(f"DEBUG: State keys: {list(state.keys())}")

                    logger.info(f"Loading trainable segmentation state for {image_id} from {old_state_path}")

                    # Process each key in the state dictionary
                    for key, value in list(state.items()):
                        # Check if the value is a string reference to a file
                        if isinstance(value, str) and value.startswith('state/trainable_segmentation/'):
                            file_path = os.path.join(self.temp_dir, value)
                            print(f"DEBUG: Found file reference for {key}: {file_path}")

                            # Handle NPZ files (NumPy arrays)
                            if file_path.endswith('.npz') and os.path.exists(file_path):
                                try:
                                    print(f"DEBUG: Loading NumPy array from {file_path}")
                                    with np.load(file_path, allow_pickle=True) as data:
                                        # The key in the NPZ file should match the key in the state dictionary
                                        if key in data:
                                            state[key] = data[key]
                                            print(f"DEBUG: Successfully loaded NumPy array for {key} with shape {state[key].shape}")
                                        else:
                                            # Try to get the first array in the file
                                            array_keys = list(data.keys())
                                            if array_keys:
                                                state[key] = data[array_keys[0]]
                                                print(f"DEBUG: Loaded NumPy array for {key} using key {array_keys[0]} with shape {state[key].shape}")
                                            else:
                                                print(f"DEBUG: No arrays found in {file_path}")
                                                state[key] = None
                                    logger.info(f"Loaded NumPy array from {file_path}")
                                except Exception as e:
                                    logger.error(f"Failed to load NumPy array for {key}: {e}")
                                    print(f"DEBUG: Failed to load NumPy array for {key}: {e}")
                                    state[key] = None

                            # Handle pickle files
                            elif file_path.endswith('.pkl') and os.path.exists(file_path):
                                try:
                                    print(f"DEBUG: Loading pickle from {file_path}")
                                    with open(file_path, 'rb') as f:
                                        state[key] = pickle.load(f)
                                    print(f"DEBUG: Successfully loaded pickle for {key}")
                                    logger.info(f"Loaded pickle from {file_path}")
                                except Exception as e:
                                    logger.error(f"Failed to load pickle for {key}: {e}")
                                    print(f"DEBUG: Failed to load pickle for {key}: {e}")
                                    state[key] = None

                            # Handle missing files
                            elif not os.path.exists(file_path):
                                logger.warning(f"File not found: {file_path}")
                                print(f"DEBUG: File not found: {file_path}")
                                state[key] = None

                    # Store in memory
                    self.trainable_segmentation_states[image_id] = state
                    logger.info(f"Successfully loaded trainable segmentation state for {image_id}")
                    return state
                except Exception as e:
                    logger.exception(f"Failed to load trainable segmentation state: {e}")
                    print(f"DEBUG: Failed to load trainable segmentation state: {e}")

        return None

    def save_image_enhancement_state(self, image_id: str, state: Dict[str, Any]):
        """Save the image enhancement state for an image."""
        self.image_enhancement_states[image_id] = state

        # Save state to disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "image_enhancement")
            os.makedirs(state_dir, exist_ok=True)

            # Save serializable data to JSON
            serializable_state = {}
            for key, value in state.items():
                if key == 'processed_image' and value is not None:
                    # Save processed image to NPZ
                    proc_path = os.path.join(state_dir, f"{image_id}_processed.npz")
                    np.savez_compressed(proc_path, processed_image=value)
                    serializable_state[key] = f"state/image_enhancement/{image_id}_processed.npz"
                else:
                    # Try to serialize directly
                    try:
                        json.dumps({key: value})
                        serializable_state[key] = value
                    except (TypeError, OverflowError):
                        logger.warning(f"Could not serialize {key} in image enhancement state")

            # Save serializable state to JSON
            state_path = os.path.join(state_dir, f"{image_id}_state.json")
            with open(state_path, 'w') as f:
                json.dump(serializable_state, f, indent=4)

    def load_image_enhancement_state(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load the image enhancement state for an image."""
        # Check if state is already in memory
        if image_id in self.image_enhancement_states:
            return self.image_enhancement_states[image_id]

        # Try to load state from disk
        if self.temp_dir:
            state_dir = os.path.join(self.temp_dir, "state", "image_enhancement")
            state_path = os.path.join(state_dir, f"{image_id}_state.json")

            if os.path.exists(state_path):
                try:
                    with open(state_path, 'r') as f:
                        state = json.load(f)

                    # Load processed image if referenced
                    if 'processed_image' in state and isinstance(state['processed_image'], str):
                        proc_path = os.path.join(self.temp_dir, state['processed_image'])
                        if os.path.exists(proc_path):
                            with np.load(proc_path) as data:
                                state['processed_image'] = data['processed_image']

                    # Store in memory
                    self.image_enhancement_states[image_id] = state
                    return state
                except Exception as e:
                    logger.exception(f"Failed to load image enhancement state: {e}")

        return None

    def save(self, project_file: str = None) -> bool:
        """Save the project to a .vlp file.

        Args:
            project_file: Path to save the project file, or None to use the current project_file.

        Returns:
            True if successful, False otherwise.
        """
        if project_file:
            self.project_file = project_file

        if not self.project_file:
            logger.error("No project file specified")
            return False

        # Ensure the project file has .vlp extension
        if not self.project_file.lower().endswith('.vlp'):
            self.project_file += '.vlp'

        # Create project directory if it doesn't exist
        if not self.temp_dir:
            self._create_project_directory()

        # Update modified timestamp
        self.modified_at = datetime.now().isoformat()

        # Create project manifest
        manifest = {
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at,
            'modified_at': self.modified_at,
            'images': {img_id: info.to_dict() for img_id, info in self.images.items()},
            'format_version': '1.0'
        }

        # Save manifest to temp directory
        manifest_path = os.path.join(self.temp_dir, "manifest.json")
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=4)

        # Backup creation disabled for performance reasons

        # Create zip file
        try:
            # First save to a temporary file to avoid corrupting the original if something goes wrong
            temp_project_file = f"{self.project_file}.tmp"

            with zipfile.ZipFile(temp_project_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Walk through the temp directory and add all files
                for root, _, files in os.walk(self.temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Get path relative to temp directory
                        rel_path = os.path.relpath(file_path, self.temp_dir)
                        zipf.write(file_path, rel_path)

            # If the temporary file was created successfully, replace the original
            if os.path.exists(temp_project_file):
                # On Windows, we need to remove the original file first
                if os.path.exists(self.project_file):
                    os.remove(self.project_file)
                os.rename(temp_project_file, self.project_file)

            logger.info(f"Project saved to {self.project_file}")
            return True
        except Exception as e:
            logger.exception(f"Failed to save project: {e}")
            return False

    def load(self, project_file: str) -> bool:
        """Load a project from a .vlp file.

        Args:
            project_file: Path to the .vlp project file.

        Returns:
            True if successful, False otherwise.
        """
        if not os.path.exists(project_file):
            logger.error(f"Project file not found: {project_file}")
            return False

        # Create project directory
        self._create_project_directory()

        # Extract zip file to temp directory
        try:
            with zipfile.ZipFile(project_file, 'r') as zipf:
                zipf.extractall(self.temp_dir)

            # Load manifest
            manifest_path = os.path.join(self.temp_dir, "manifest.json")
            if not os.path.exists(manifest_path):
                logger.error(f"Manifest file not found in project: {project_file}")
                return False

            with open(manifest_path, 'r') as f:
                manifest = json.load(f)

            # Update project attributes
            self.project_file = project_file
            self.name = manifest.get('name', os.path.basename(project_file).replace('.vlp', ''))
            self.description = manifest.get('description', "")
            self.created_at = manifest.get('created_at', datetime.now().isoformat())
            self.modified_at = manifest.get('modified_at', self.created_at)

            # Load images
            self.images = {}
            for img_id, img_data in manifest.get('images', {}).items():
                self.images[img_id] = ImageInfo.from_dict(img_data)

            logger.info(f"Project loaded from {project_file}")
            return True
        except Exception as e:
            logger.exception(f"Failed to load project: {e}")
            return False

    def close(self):
        """Close the project.

        Note: We no longer delete the project directory to ensure state is preserved between sessions.
        """
        # Only clean up if it's a temporary directory (no project file)
        if not self.project_file and self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
                logger.info("Project temporary files cleaned up")
            except Exception as e:
                logger.exception(f"Failed to clean up temporary files: {e}")
        else:
            # Just set the directory to None without deleting it
            self.temp_dir = None
            logger.info("Project closed (persistent directory preserved)")

    def __del__(self):
        """Destructor to ensure temporary files are cleaned up."""
        self.close()

    @staticmethod
    def convert_from_legacy(legacy_project_dir: str, output_file: str) -> bool:
        """Convert a legacy project to the new .vlp format.

        Args:
            legacy_project_dir: Path to the legacy project directory.
            output_file: Path to save the new .vlp project file.

        Returns:
            True if successful, False otherwise.
        """
        try:
            # Load legacy project
            legacy_project = Project.load(legacy_project_dir)

            # Create new project
            new_project = GrainSightProject()
            new_project.name = legacy_project.name
            new_project.description = legacy_project.description
            new_project.created_at = legacy_project.created_at
            new_project.modified_at = legacy_project.modified_at

            # Copy images
            for img_id, img_info in legacy_project.images.items():
                legacy_img_path = os.path.join(legacy_project_dir, img_info.filepath)
                if os.path.exists(legacy_img_path):
                    new_project.add_image(legacy_img_path)

            # Save new project
            if new_project.save(output_file):
                logger.info(f"Legacy project converted to {output_file}")
                return True
            else:
                logger.error("Failed to save converted project")
                return False
        except Exception as e:
            logger.exception(f"Failed to convert legacy project: {e}")
            return False
