"""
2: Zoomable Graphics View Widget for VisionLab Ai.
This module provides a QGraphicsView subclass that supports mouse wheel zooming.
"""

from PySide6.QtWidgets import QGraphicsView
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPainter

class ZoomableGraphicsView(QGraphicsView):
    """A QGraphicsView subclass that supports mouse wheel zooming."""
    
    # Signal emitted when zoom level changes
    zoomChanged = Signal(float)
    
    def __init__(self, scene=None, parent=None):
        """Initialize the zoomable graphics view."""
        super().__init__(scene, parent)
        
        # Set rendering hints for better quality
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # Set transformation anchors to mouse position
        self.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.ViewportAnchor.AnchorUnderMouse)
        
        # Set drag mode to scroll hand
        self.setDragMode(QGraphicsView.DragMode.ScrollHandDrag)
        
        # Initialize zoom level
        self.zoom_level = 1.0
        
        # Zoom factors
        self.zoom_factor_in = 1.15
        self.zoom_factor_out = 1 / 1.15
        
        # Enable mouse tracking
        self.setMouseTracking(True)
        
    def wheelEvent(self, event):
        """Handle mouse wheel events for zooming."""
        try:
            # Get the mouse position in scene coordinates before scaling
            old_pos = self.mapToScene(event.position().toPoint())
            
            # Determine the zoom direction and calculate the factor
            delta = event.angleDelta().y()
            factor = self.zoom_factor_in if delta > 0 else self.zoom_factor_out
            
            # Update zoom level
            self.zoom_level *= factor
            
            # Apply the zoom
            self.scale(factor, factor)
            
            # Emit signal with new zoom level
            self.zoomChanged.emit(self.zoom_level)
            
            # Accept the event to prevent it from being passed to the parent
            event.accept()
        except Exception as e:
            print(f"Error in ZoomableGraphicsView.wheelEvent: {e}")
            super().wheelEvent(event)
    
    def zoomIn(self):
        """Zoom in by a fixed factor."""
        self.scale(self.zoom_factor_in, self.zoom_factor_in)
        self.zoom_level *= self.zoom_factor_in
        self.zoomChanged.emit(self.zoom_level)
    
    def zoomOut(self):
        """Zoom out by a fixed factor."""
        self.scale(self.zoom_factor_out, self.zoom_factor_out)
        self.zoom_level *= self.zoom_factor_out
        self.zoomChanged.emit(self.zoom_level)
    
    def resetZoom(self):
        """Reset zoom to original level."""
        self.resetTransform()
        self.zoom_level = 1.0
        self.zoomChanged.emit(self.zoom_level)
    
    def fitInView(self, rect, mode=Qt.AspectRatioMode.KeepAspectRatio):
        """Fit the view to the given rectangle."""
        super().fitInView(rect, mode)
        # Estimate the new zoom level based on the transformation
        self.zoom_level = self.transform().m11()  # Use the horizontal scale factor
        self.zoomChanged.emit(self.zoom_level)
