#!/usr/bin/env python3
"""
Test script for the synchronized side-by-side view in the unsupervised segmentation page.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt
import numpy as np
from PIL import Image as PILImage

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import our modified ProcessPageUI
from gui.ui.process_page_ui import ProcessPageUI

class TestWindow(QMainWindow, ProcessPageUI):
    """Test window to demonstrate the synchronized side-by-side view."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Synchronized Side-by-Side View")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create a central widget and stacked widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create a mock stacked widget
        from PySide6.QtWidgets import QTabWidget
        self.stacked_widget = QTabWidget()
        layout.addWidget(self.stacked_widget)
        
        # Setup the process page
        self.setup_process_page()
        
        # Create test images
        self.create_test_images()
        
    def create_test_images(self):
        """Create test images to demonstrate synchronization."""
        # Create a test original image (gradient)
        width, height = 800, 600
        original_array = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Create a gradient pattern
        for y in range(height):
            for x in range(width):
                original_array[y, x] = [x * 255 // width, y * 255 // height, 128]
        
        # Convert to QPixmap
        original_image = PILImage.fromarray(original_array)
        original_qimage = QImage(original_array.data, width, height, QImage.Format_RGB888)
        original_pixmap = QPixmap.fromImage(original_qimage)
        
        # Create a test segmented image (different pattern)
        segmented_array = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Create a checkerboard pattern
        block_size = 50
        for y in range(height):
            for x in range(width):
                if ((x // block_size) + (y // block_size)) % 2 == 0:
                    segmented_array[y, x] = [255, 0, 0]  # Red
                else:
                    segmented_array[y, x] = [0, 255, 0]  # Green
        
        # Convert to QPixmap
        segmented_qimage = QImage(segmented_array.data, width, height, QImage.Format_RGB888)
        segmented_pixmap = QPixmap.fromImage(segmented_qimage)
        
        # Set the images in both views
        self.original_image_view.setPixmap(original_pixmap)
        self.segmented_image_view.setPixmap(segmented_pixmap)
        
        print("Test images loaded successfully!")
        print("Try zooming and panning in either view - they should stay synchronized!")

def main():
    """Main function to run the test."""
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = TestWindow()
    window.show()
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()