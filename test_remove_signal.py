#!/usr/bin/env python3
"""
Test script to verify the trainable segmentation remove signal implementation.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer
from src.widgets.page_image_gallery import PageImageGallery
from src.widgets.trainable_segmentation_gallery import TrainableSegmentationGallery
from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers
import numpy as np
from PySide6.QtGui import QPixmap

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Remove Signal Implementation")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create status label
        self.status_label = QLabel("Testing remove signal implementation...")
        layout.addWidget(self.status_label)
        
        # Create trainable gallery
        self.trainable_gallery = TrainableSegmentationGallery()
        layout.addWidget(self.trainable_gallery)
        
        # Create test button
        self.test_button = QPushButton("Add Test Images")
        self.test_button.clicked.connect(self.add_test_images)
        layout.addWidget(self.test_button)
        
        # Connect the remove signal to our test handler
        self.trainable_gallery.remove_clicked.connect(self.on_remove_clicked)
        
        # Track removed images
        self.removed_images = []
        
    def add_test_images(self):
        """Add some test images to the gallery."""
        for i in range(3):
            # Create a simple test image (colored rectangle)
            image_array = np.full((100, 100, 3), [255 * (i % 2), 128, 255 * ((i + 1) % 2)], dtype=np.uint8)
            filename = f"test_image_{i+1}.png"
            filepath = f"/test/path/test_image_{i+1}.png"
            
            self.trainable_gallery.add_image(image_array, filename, filepath)
            
        self.status_label.setText(f"Added 3 test images. Click X buttons to test removal.")
        
    def on_remove_clicked(self, index):
        """Handler for when remove button is clicked."""
        if index < len(self.trainable_gallery.file_paths):
            filename = self.trainable_gallery.filenames[index]
            self.removed_images.append(filename)
            self.status_label.setText(f"Remove signal received for index {index}: {filename}. Total removed: {len(self.removed_images)}")
            print(f"TEST: Remove signal received for index {index}: {filename}")
        else:
            self.status_label.setText(f"ERROR: Invalid index {index} for removal")
            print(f"TEST ERROR: Invalid index {index} for removal")

def main():
    app = QApplication(sys.argv)
    
    # Test 1: Check if signals exist
    print("TEST 1: Checking if signals exist...")
    gallery = TrainableSegmentationGallery()
    
    if hasattr(gallery, 'remove_clicked'):
        print("✓ TrainableSegmentationGallery has remove_clicked signal")
    else:
        print("✗ TrainableSegmentationGallery missing remove_clicked signal")
        
    if hasattr(gallery, 'image_clicked'):
        print("✓ TrainableSegmentationGallery has image_clicked signal")
    else:
        print("✗ TrainableSegmentationGallery missing image_clicked signal")
    
    # Test 2: Check if handler method exists
    print("\nTEST 2: Checking if handler method exists...")
    try:
        from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers
        if hasattr(TrainableSegmentationHandlers, 'on_trainable_gallery_remove_clicked'):
            print("✓ TrainableSegmentationHandlers has on_trainable_gallery_remove_clicked method")
        else:
            print("✗ TrainableSegmentationHandlers missing on_trainable_gallery_remove_clicked method")
    except Exception as e:
        print(f"✗ Error importing TrainableSegmentationHandlers: {e}")
    
    # Test 3: Interactive test
    print("\nTEST 3: Starting interactive test window...")
    window = TestWindow()
    window.show()
    
    # Auto-close after 30 seconds for automated testing
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(30000)  # 30 seconds
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())