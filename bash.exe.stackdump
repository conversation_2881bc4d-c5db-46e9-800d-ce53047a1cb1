Stack trace:
Frame         Function      Args
0007FFFFA350  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA350, 0007FFFF9250) msys-2.0.dll+0x1FE8E
0007FFFFA350  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA628) msys-2.0.dll+0x67F9
0007FFFFA350  000210046832 (000210286019, 0007FFFFA208, 0007FFFFA350, 000000000000) msys-2.0.dll+0x6832
0007FFFFA350  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA350  000210068E24 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA630  00021006A225 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD3F2A0000 ntdll.dll
7FFD3D550000 KERNEL32.DLL
7FFD3CAC0000 KERNELBASE.dll
7FFD3EA20000 USER32.dll
000210040000 msys-2.0.dll
7FFD3C580000 win32u.dll
7FFD3D340000 GDI32.dll
7FFD3CF20000 gdi32full.dll
7FFD3C4D0000 msvcp_win.dll
7FFD3C5B0000 ucrtbase.dll
7FFD3E200000 advapi32.dll
7FFD3E870000 msvcrt.dll
7FFD3F180000 sechost.dll
7FFD3D060000 RPCRT4.dll
7FFD3BA20000 CRYPTBASE.DLL
7FFD3C430000 bcryptPrimitives.dll
7FFD3D280000 IMM32.DLL
