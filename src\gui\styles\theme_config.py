"""
Theme configuration for VisionLab Ai application.
This module defines the theme structure and default themes.
"""

from PySide6.QtGui import QPalette, QColor, QFont
from PySide6.QtCore import Qt

# Define default font sizes
FONT_SIZES = {
    "small": 8,
    "normal": 10,
    "medium": 12,
    "large": 14,
    "extra-large": 16
}

# Define UI style parameters with defaults
UI_STYLE_PARAMS = {
    "border-radius": 4,       # Default border radius for controls
    "button-radius": 4,      # Button-specific border radius
    "padding": 2,           # Default padding for controls
    "shadow-strength": 15,   # Shadow opacity (0-100)
    "animation-speed": 150,  # Animation duration in ms
    "opacity": 100,         # Opacity percentage (0-100)
    "control-height": 28,    # Default height for controls
    "slider-height": 2,      # Height for sliders (reduced from 8)
    "slider-handle-size": 12, # Size of slider handles (reduced from 18)
    "slider-groove-color": "#CCCCCC",  # Color for slider track
    "slider-handle-color": "#0078d4",  # Color for slider handle
    "section-border-width": 1,         # Width of section borders (minimum visible width)
    "section-border-radius": 3,        # Border radius for sections
    "section-border-opacity": 30,      # Opacity of section borders (0-100)
    "section-accent-opacity": 0.4,     # Opacity of section accent colors
    "section-gradient-strength": 1     # Strength of section background gradients
}

# Define color schemes
COLOR_SCHEMES = {
    # Modern Dark Themes
    "default-dark": {
        "window": QColor(28, 28, 30),  # Darker background
        "window-text": QColor(255, 255, 255),
        "base": QColor(18, 18, 20),  # Even darker for contrast
        "alternate-base": QColor(38, 38, 40),
        "tooltip-base": QColor(45, 45, 48),
        "tooltip-text": QColor(255, 255, 255),
        "text": QColor(255, 255, 255),
        "button": QColor(60, 60, 65),
        "button-text": QColor(255, 255, 255),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(65, 155, 255),
        "highlight": QColor(0, 122, 255),  # iOS blue
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(10, 132, 255),  # Brighter accent
    },
    "blue-dark": {
        "window": QColor(22, 27, 34),  # GitHub Dark
        "window-text": QColor(230, 237, 243),
        "base": QColor(13, 17, 23),
        "alternate-base": QColor(33, 38, 45),
        "tooltip-base": QColor(33, 38, 45),
        "tooltip-text": QColor(230, 237, 243),
        "text": QColor(230, 237, 243),
        "button": QColor(33, 38, 45),
        "button-text": QColor(230, 237, 243),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(88, 166, 255),
        "highlight": QColor(56, 139, 253),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(88, 166, 255),
    },
    "green-dark": {
        "window": QColor(18, 30, 18),
        "window-text": QColor(230, 255, 230),
        "base": QColor(12, 20, 12),
        "alternate-base": QColor(28, 40, 28),
        "tooltip-base": QColor(28, 40, 28),
        "tooltip-text": QColor(230, 255, 230),
        "text": QColor(230, 255, 230),
        "button": QColor(35, 50, 35),
        "button-text": QColor(230, 255, 230),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(76, 217, 100),  # iOS green
        "highlight": QColor(52, 199, 89),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(76, 217, 100),
    },
    "purple-dark": {
        "window": QColor(30, 25, 40),
        "window-text": QColor(240, 230, 255),
        "base": QColor(22, 18, 30),
        "alternate-base": QColor(40, 35, 50),
        "tooltip-base": QColor(40, 35, 50),
        "tooltip-text": QColor(240, 230, 255),
        "text": QColor(240, 230, 255),
        "button": QColor(50, 45, 60),
        "button-text": QColor(240, 230, 255),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(175, 82, 222),  # iOS purple
        "highlight": QColor(150, 70, 200),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(175, 82, 222),
    },
    "orange-dark": {
        "window": QColor(35, 25, 20),
        "window-text": QColor(255, 240, 230),
        "base": QColor(25, 18, 15),
        "alternate-base": QColor(45, 35, 30),
        "tooltip-base": QColor(45, 35, 30),
        "tooltip-text": QColor(255, 240, 230),
        "text": QColor(255, 240, 230),
        "button": QColor(55, 45, 40),
        "button-text": QColor(255, 240, 230),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(255, 149, 0),  # iOS orange
        "highlight": QColor(230, 130, 20),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(255, 149, 0),
    },
    "teal-dark": {
        "window": QColor(20, 35, 35),
        "window-text": QColor(230, 255, 255),
        "base": QColor(15, 25, 25),
        "alternate-base": QColor(30, 45, 45),
        "tooltip-base": QColor(30, 45, 45),
        "tooltip-text": QColor(230, 255, 255),
        "text": QColor(230, 255, 255),
        "button": QColor(40, 55, 55),
        "button-text": QColor(230, 255, 255),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(90, 200, 250),  # iOS teal
        "highlight": QColor(70, 180, 230),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(90, 200, 250),
    },
    "monochrome-dark": {
        "window": QColor(18, 18, 18),
        "window-text": QColor(240, 240, 240),
        "base": QColor(10, 10, 10),
        "alternate-base": QColor(30, 30, 30),
        "tooltip-base": QColor(30, 30, 30),
        "tooltip-text": QColor(240, 240, 240),
        "text": QColor(240, 240, 240),
        "button": QColor(40, 40, 40),
        "button-text": QColor(240, 240, 240),
        "bright-text": QColor(255, 255, 255),
        "link": QColor(180, 180, 180),
        "highlight": QColor(100, 100, 100),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(150, 150, 150),
    },

    # Modern Light Themes
    "default-light": {
        "window": QColor(248, 248, 248),  # Lighter background
        "window-text": QColor(0, 0, 0),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(238, 238, 238),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(0, 0, 0),
        "text": QColor(0, 0, 0),
        "button": QColor(240, 240, 240),
        "button-text": QColor(0, 0, 0),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(0, 122, 255),  # iOS blue
        "highlight": QColor(0, 122, 255),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(10, 132, 255),
    },
    "blue-light": {
        "window": QColor(246, 248, 250),  # GitHub Light
        "window-text": QColor(36, 41, 47),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(240, 242, 245),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(36, 41, 47),
        "text": QColor(36, 41, 47),
        "button": QColor(240, 242, 245),
        "button-text": QColor(36, 41, 47),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(9, 105, 218),
        "highlight": QColor(0, 122, 255),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(9, 105, 218),
    },
    "green-light": {
        "window": QColor(245, 255, 245),
        "window-text": QColor(20, 50, 20),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(235, 245, 235),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(20, 50, 20),
        "text": QColor(20, 50, 20),
        "button": QColor(235, 245, 235),
        "button-text": QColor(20, 50, 20),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(52, 199, 89),  # iOS green
        "highlight": QColor(52, 199, 89),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(52, 199, 89),
    },
    "purple-light": {
        "window": QColor(250, 245, 255),
        "window-text": QColor(50, 20, 70),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(240, 235, 250),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(50, 20, 70),
        "text": QColor(50, 20, 70),
        "button": QColor(240, 235, 250),
        "button-text": QColor(50, 20, 70),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(175, 82, 222),  # iOS purple
        "highlight": QColor(175, 82, 222),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(175, 82, 222),
    },
    "orange-light": {
        "window": QColor(255, 250, 245),
        "window-text": QColor(70, 40, 10),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(250, 240, 230),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(70, 40, 10),
        "text": QColor(70, 40, 10),
        "button": QColor(250, 240, 230),
        "button-text": QColor(70, 40, 10),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(255, 149, 0),  # iOS orange
        "highlight": QColor(255, 149, 0),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(255, 149, 0),
    },
    "teal-light": {
        "window": QColor(245, 255, 255),
        "window-text": QColor(10, 60, 70),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(235, 250, 250),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(10, 60, 70),
        "text": QColor(10, 60, 70),
        "button": QColor(235, 250, 250),
        "button-text": QColor(10, 60, 70),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(90, 200, 250),  # iOS teal
        "highlight": QColor(90, 200, 250),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(90, 200, 250),
    },
    "monochrome-light": {
        "window": QColor(250, 250, 250),
        "window-text": QColor(30, 30, 30),
        "base": QColor(255, 255, 255),
        "alternate-base": QColor(240, 240, 240),
        "tooltip-base": QColor(255, 255, 255),
        "tooltip-text": QColor(30, 30, 30),
        "text": QColor(30, 30, 30),
        "button": QColor(240, 240, 240),
        "button-text": QColor(30, 30, 30),
        "bright-text": QColor(0, 0, 0),
        "link": QColor(100, 100, 100),
        "highlight": QColor(150, 150, 150),
        "highlight-text": QColor(255, 255, 255),
        "accent": QColor(100, 100, 100),
    }
}

# Define font families
FONT_FAMILIES = [
    "Segoe UI",
    "Arial",
    "Helvetica",
    "Verdana",
    "Tahoma",
    "Calibri",
    "Times New Roman",
    "Courier New",
    "Consolas",
    "System"
]

def create_palette_from_scheme(scheme_name):
    """Creates a QPalette from a color scheme."""
    if scheme_name not in COLOR_SCHEMES:
        scheme_name = "default-dark" if "dark" in scheme_name.lower() else "default-light"

    scheme = COLOR_SCHEMES[scheme_name]
    palette = QPalette()

    # Set palette colors
    palette.setColor(QPalette.ColorRole.Window, scheme["window"])
    palette.setColor(QPalette.ColorRole.WindowText, scheme["window-text"])
    palette.setColor(QPalette.ColorRole.Base, scheme["base"])
    palette.setColor(QPalette.ColorRole.AlternateBase, scheme["alternate-base"])
    palette.setColor(QPalette.ColorRole.ToolTipBase, scheme["tooltip-base"])
    palette.setColor(QPalette.ColorRole.ToolTipText, scheme["tooltip-text"])
    palette.setColor(QPalette.ColorRole.Text, scheme["text"])
    palette.setColor(QPalette.ColorRole.Button, scheme["button"])
    palette.setColor(QPalette.ColorRole.ButtonText, scheme["button-text"])
    palette.setColor(QPalette.ColorRole.BrightText, scheme["bright-text"])
    palette.setColor(QPalette.ColorRole.Link, scheme["link"])
    palette.setColor(QPalette.ColorRole.Highlight, scheme["highlight"])
    palette.setColor(QPalette.ColorRole.HighlightedText, scheme["highlight-text"])

    return palette

def get_stylesheet_for_scheme(scheme_name, font_family, font_size, style_params=None):
    """Returns a stylesheet for the given color scheme and font settings."""
    if scheme_name not in COLOR_SCHEMES:
        scheme_name = "default-dark" if "dark" in scheme_name.lower() else "default-light"

    scheme = COLOR_SCHEMES[scheme_name]
    is_dark = "dark" in scheme_name.lower()

    # Use default style parameters if none provided
    if style_params is None:
        style_params = UI_STYLE_PARAMS.copy()

    # Extract style parameters
    border_radius = style_params.get("border-radius", 4)
    button_radius = style_params.get("button-radius", 6)
    padding = style_params.get("padding", 6)
    shadow_strength = style_params.get("shadow-strength", 15)
    animation_speed = style_params.get("animation-speed", 150)
    opacity = style_params.get("opacity", 100)
    control_height = style_params.get("control-height", 28)
    slider_height = style_params.get("slider-height", 2)
    slider_handle_size = style_params.get("slider-handle-size", 12)
    slider_groove_color = style_params.get("slider-groove-color", "#CCCCCC")
    slider_handle_color = style_params.get("slider-handle-color", "#0078d4")

    # Calculate shadow color and opacity
    shadow_color = "#000000" if is_dark else "#888888"
    shadow_opacity = shadow_strength / 100.0
    shadow_blur = int(shadow_strength / 5)

    # Convert colors to hex strings
    colors = {key: color.name() for key, color in scheme.items()}

    # Base stylesheet with font settings
    stylesheet = f"""
    QWidget {{  /* Base widget style */
        font-family: "{font_family}";
        font-size: {font_size}pt;
        background-color: {colors["window"]};
        color: {colors["window-text"]};
        selection-background-color: {colors["highlight"]};
        selection-color: {colors["highlight-text"]};
    }}

    QMainWindow, QDialog {{  /* Main window and dialog style */
        background-color: {colors["window"]};
        color: {colors["window-text"]};
    }}

    /* Modern GroupBox with colored accents */
    QGroupBox {{
        /* Use opacity for border color */
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["accent"].red()}, {scheme["accent"].green()}, {scheme["accent"].blue()}, {style_params.get("section-border-opacity", 30)/100});
        border-radius: {style_params.get("section-border-radius", 3)}px;
        margin-top: 14px;
        padding-top: 8px;
        padding: 10px;
        font-weight: bold;
        background-color: {scheme["window"].lighter(102).name() if is_dark else scheme["window"].darker(102).name()};
        /* Add subtle gradient */
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(103 + style_params.get("section-gradient-strength", 1)).name() if is_dark else scheme["window"].darker(103 + style_params.get("section-gradient-strength", 1)).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Image Gallery sections - special styling with accent color */
    QGroupBox[title="Image Gallery"], QGroupBox[title="Gallery"], QGroupBox[title*="Gallery"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["highlight"].red()}, {scheme["highlight"].green()}, {scheme["highlight"].blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(110).name() if is_dark else scheme["window"].darker(110).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Controls sections - special styling */
    QGroupBox[title="Controls"], QGroupBox[title="Parameters"], QGroupBox[title="Settings"],
    QGroupBox[title*="Parameter"], QGroupBox[title*="Control"], QGroupBox[title*="Setting"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["link"].red()}, {scheme["link"].green()}, {scheme["link"].blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(108).name() if is_dark else scheme["window"].darker(108).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Results sections - special styling */
    QGroupBox[title*="Result"], QGroupBox[title*="Statistics"], QGroupBox[title*="Analysis"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["highlight"].lighter(120).red()}, {scheme["highlight"].lighter(120).green()}, {scheme["highlight"].lighter(120).blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(106).name() if is_dark else scheme["window"].darker(106).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Feature sections - special styling */
    QGroupBox[title*="Feature"], QGroupBox[title="Feature Selection"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["accent"].red()}, {scheme["accent"].green()}, {scheme["accent"].blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(107).name() if is_dark else scheme["window"].darker(107).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Method sections - special styling */
    QGroupBox[title="Method"], QGroupBox[title*="Method"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["link"].lighter(110).red()}, {scheme["link"].lighter(110).green()}, {scheme["link"].lighter(110).blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(109).name() if is_dark else scheme["window"].darker(109).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Segmentation sections - special styling */
    QGroupBox[title*="Segmentation"], QGroupBox[title*="Label"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["accent"].lighter(115).red()}, {scheme["accent"].lighter(115).green()}, {scheme["accent"].lighter(115).blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(107).name() if is_dark else scheme["window"].darker(107).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    /* Classes sections - special styling */
    QGroupBox[title="Classes"], QGroupBox[title*="Class"] {{
        border: {style_params.get("section-border-width", 1)}px solid rgba({scheme["highlight"].lighter(130).red()}, {scheme["highlight"].lighter(130).green()}, {scheme["highlight"].lighter(130).blue()}, {style_params.get("section-border-opacity", 30)/100});
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 {scheme["window"].lighter(108).name() if is_dark else scheme["window"].darker(108).name()},
                                  stop:1 {scheme["window"].name()});
    }}

    QGroupBox::title {{  /* Accent-colored title */
        subcontrol-origin: margin;
        subcontrol-position: top left;
        left: 10px;
        padding: 0 5px;
        color: {colors["accent"]};
        font-weight: bold;
    }}

    /* Special title colors for different section types */
    QGroupBox[title="Image Gallery"]::title, QGroupBox[title="Gallery"]::title, QGroupBox[title*="Gallery"]::title {{
        color: {colors["highlight"]};
    }}

    QGroupBox[title="Controls"]::title, QGroupBox[title="Parameters"]::title, QGroupBox[title="Settings"]::title,
    QGroupBox[title*="Parameter"]::title, QGroupBox[title*="Control"]::title, QGroupBox[title*="Setting"]::title {{
        color: {colors["link"]};
    }}

    QGroupBox[title*="Result"]::title, QGroupBox[title*="Statistics"]::title, QGroupBox[title*="Analysis"]::title {{
        color: {scheme["highlight"].lighter(120).name() if is_dark else scheme["highlight"].darker(120).name()};
    }}

    QGroupBox[title*="Feature"]::title, QGroupBox[title="Feature Selection"]::title {{
        color: {colors["accent"]};
    }}

    QGroupBox[title="Method"]::title, QGroupBox[title*="Method"]::title {{
        color: {scheme["link"].lighter(110).name() if is_dark else scheme["link"].darker(110).name()};
    }}

    QGroupBox[title*="Segmentation"]::title, QGroupBox[title*="Label"]::title {{
        color: {scheme["accent"].lighter(115).name() if is_dark else scheme["accent"].darker(115).name()};
    }}

    QGroupBox[title="Classes"]::title, QGroupBox[title*="Class"]::title {{
        color: {scheme["highlight"].lighter(130).name() if is_dark else scheme["highlight"].darker(130).name()};
    }}

    /* Modern button style with hover effects */
    QPushButton {{
        background-color: {colors["button"]};
        color: {colors["button-text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {button_radius}px;
        padding: {padding}px {padding*2}px;
        min-height: {control_height}px;
        text-align: center;
    }}

    QPushButton:hover {{  /* Smooth hover effect */
        background-color: {scheme["button"].lighter(110).name()};
        border: 1px solid {scheme["alternate-base"].lighter(110).name()};
    }}

    QPushButton:pressed {{  /* Pressed state */
        background-color: {scheme["button"].darker(110).name()};
        padding: {padding+1}px {padding*2-1}px {padding-1}px {padding*2+1}px; /* Slight shift effect */
    }}

    QPushButton:disabled {{  /* Disabled state */
        background-color: {scheme["button"].darker(120).name() if is_dark else scheme["button"].lighter(120).name()};
        color: {scheme["button-text"].darker(150).name() if is_dark else scheme["button-text"].lighter(150).name()};
        border: 1px solid {scheme["alternate-base"].darker(110).name() if is_dark else scheme["alternate-base"].lighter(110).name()};
    }}

    /* Modern text input fields */
    QLineEdit, QTextEdit, QPlainTextEdit {{
        background-color: {colors["base"]};
        color: {colors["text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        padding: {padding-2}px;
        selection-background-color: {colors["highlight"]};
        selection-color: {colors["highlight-text"]};
        min-height: {control_height-4}px;
    }}

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{  /* Focus effect */
        border: 1px solid {colors["highlight"]};
    }}

    /* Modern dropdown style */
    QComboBox {{
        background-color: {colors["button"]};
        color: {colors["button-text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        padding: {padding-2}px {padding+2}px;
        min-height: {control_height}px;
    }}

    QComboBox:hover {{  /* Hover effect */
        background-color: {scheme["button"].lighter(110).name()};
        border: 1px solid {scheme["alternate-base"].lighter(110).name()};
    }}

    QComboBox:on {{  /* When dropdown is open */
        background-color: {scheme["button"].darker(105).name()};
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
    }}

    QComboBox::drop-down {{  /* Dropdown button */
        subcontrol-origin: padding;
        subcontrol-position: right center;
        width: 20px;
        border-left: 1px solid {colors["alternate-base"]};
        border-top-right-radius: {border_radius-1}px;
        border-bottom-right-radius: {border_radius-1}px;
        background-color: {colors["button"]};  /* Ensure the dropdown button is visible */
    }}

    QComboBox::down-arrow {{  /* Dropdown arrow */
        width: 10px;
        height: 10px;
        image: none;  /* Remove any existing arrow image */
        /* Draw a triangle using borders */
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {colors["text"]};
    }}

    QComboBox QAbstractItemView {{  /* Dropdown menu */
        background-color: {colors["base"]};
        color: {colors["text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        selection-background-color: {colors["highlight"]};
        selection-color: {colors["highlight-text"]};
        padding: 2px;
    }}

    /* Modern tab widget */
    QTabWidget::pane {{  /* Tab content area */
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        top: -1px;
    }}

    QTabBar::tab {{  /* Individual tabs */
        background-color: {colors["button"]};
        color: {colors["button-text"]};
        border: 1px solid {colors["alternate-base"]};
        border-bottom-color: {colors["alternate-base"]};
        border-top-left-radius: {border_radius}px;
        border-top-right-radius: {border_radius}px;
        padding: {padding}px {padding*2}px;
        margin-right: 2px;
        min-width: 80px;
        min-height: {control_height-6}px;
    }}

    QTabBar::tab:selected {{  /* Selected tab */
        background-color: {colors["window"]};
        border-bottom-color: {colors["window"]};
    }}

    QTabBar::tab:!selected {{  /* Unselected tabs */
        margin-top: 2px;
        background-color: {scheme["button"].darker(105).name() if is_dark else scheme["button"].lighter(105).name()};
    }}

    QTabBar::tab:hover {{  /* Tab hover effect */
        background-color: {scheme["button"].lighter(110).name()};
    }}

    /* Checkboxes and radio buttons */
    QCheckBox, QRadioButton {{
        color: {colors["window-text"]};
        spacing: 8px;
        min-height: {control_height-4}px;
    }}

    QCheckBox::indicator, QRadioButton::indicator {{  /* Indicator box/circle */
        width: 16px;
        height: 16px;
        border: 1px solid {colors["alternate-base"]};
        background-color: {colors["base"]};
    }}

    QCheckBox::indicator {{  /* Checkbox shape */
        border-radius: 3px;
    }}

    QRadioButton::indicator {{  /* Radio button shape */
        border-radius: 8px;
    }}

    QCheckBox::indicator:checked, QRadioButton::indicator:checked {{  /* Checked state */
        background-color: {colors["highlight"]};
        border: 1px solid {colors["highlight"]};
    }}

    QCheckBox::indicator:hover, QRadioButton::indicator:hover {{  /* Hover effect */
        border: 1px solid {colors["highlight"]};
    }}

    /* Modern sliders */
    QSlider::groove:horizontal {{  /* Slider track */
        border: none;
        height: {slider_height}px;
        background: {slider_groove_color};
        margin: 2px 0;
        border-radius: {slider_height/2}px;
    }}

    QSlider::handle:horizontal {{  /* Slider handle */
        background: {slider_handle_color};
        border: none;
        width: {slider_handle_size}px;
        height: {slider_handle_size}px;
        margin: -{(slider_handle_size-slider_height)/2}px 0;
        border-radius: {slider_handle_size/2}px;
    }}

    QSlider::sub-page:horizontal {{  /* Filled part of slider */
        background: {slider_handle_color};
        border-radius: {slider_height/2}px;
    }}

    QSlider::handle:horizontal:hover {{  /* Handle hover effect */
        background: {slider_handle_color};
    }}

    /* Progress bars */
    QProgressBar {{
        border: none;
        border-radius: {border_radius}px;
        text-align: center;
        color: {colors["highlight-text"]};
        background-color: {scheme["alternate-base"].darker(110).name() if is_dark else scheme["alternate-base"].lighter(110).name()};
        min-height: {slider_height+4}px;
    }}

    QProgressBar::chunk {{  /* Progress indicator */
        background-color: {colors["highlight"]};
        border-radius: {border_radius}px;
    }}

    /* Modern scrollbars */
    QScrollBar:vertical {{
        background: transparent;
        width: 12px;
        margin: 12px 0 12px 0;
        border-radius: 6px;
    }}

    QScrollBar::handle:vertical {{  /* Scrollbar handle */
        background: rgba(10, 132, 255, 102); /* #0a84ff with 40% opacity */
        min-height: 20px;
        border-radius: 6px;
    }}

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{  /* Scroll buttons */
        border: none;
        background: none;
        height: 12px;
    }}

    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{  /* Background areas */
        background: none;
    }}

    QScrollBar:horizontal {{
        background: transparent;
        height: 12px;
        margin: 0 12px 0 12px;
        border-radius: 6px;
    }}

    QScrollBar::handle:horizontal {{  /* Horizontal scrollbar handle */
        background: rgba(10, 132, 255, 102); /* #0a84ff with 40% opacity */
        min-width: 20px;
        border-radius: 6px;
    }}

    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{  /* Horizontal scroll buttons */
        border: none;
        background: none;
        width: 12px;
    }}

    QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{  /* Background areas */
        background: none;
    }}

    /* Table and list views */
    QTableView, QTreeView, QListView {{
        background-color: {colors["base"]};
        alternate-background-color: {scheme["base"].darker(105).name() if is_dark else scheme["base"].lighter(105).name()};
        color: {colors["text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        selection-background-color: {colors["highlight"]};
        selection-color: {colors["highlight-text"]};
        gridline-color: {scheme["alternate-base"].darker(110).name() if is_dark else scheme["alternate-base"].lighter(110).name()};
    }}

    QHeaderView::section {{  /* Table headers */
        background-color: {colors["button"]};
        color: {colors["button-text"]};
        padding: {padding}px;
        border: 1px solid {colors["alternate-base"]};
        font-weight: bold;
    }}

    /* Tooltips */
    QToolTip {{
        background-color: {colors["tooltip-base"]};
        color: {colors["tooltip-text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        padding: 4px 8px;
        opacity: {opacity}%;
    }}

    /* Spin boxes */
    QSpinBox, QDoubleSpinBox {{
        background-color: {colors["base"]};
        color: {colors["text"]};
        border: 1px solid {colors["alternate-base"]};
        border-radius: {border_radius}px;
        padding: 2px {padding}px;
        min-height: {control_height-4}px;
    }}

    QSpinBox::up-button, QDoubleSpinBox::up-button {{  /* Up button */
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 16px;
        border-left: 1px solid {colors["alternate-base"]};
        border-bottom: 1px solid {colors["alternate-base"]};
        border-top-right-radius: {border_radius-1}px;
        background-color: {colors["button"]};
    }}

    QSpinBox::down-button, QDoubleSpinBox::down-button {{  /* Down button */
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 16px;
        border-left: 1px solid {colors["alternate-base"]};
        border-bottom-right-radius: {border_radius-1}px;
        background-color: {colors["button"]};
    }}

    QSpinBox::up-button:hover, QSpinBox::down-button:hover,
    QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {{  /* Hover effect */
        background-color: {scheme["button"].lighter(110).name()};
    }}

    QSpinBox:focus, QDoubleSpinBox:focus {{  /* Focus effect */
        border: 1px solid {colors["highlight"]};
    }}
    """

    return stylesheet

def get_palette(theme_name):
    """Returns the palette for the given theme name."""
    return create_palette_from_scheme(theme_name)

def apply_theme(widget, theme_name, font_family, font_size, style_params=None):
    """Applies the theme to the widget.

    Args:
        widget: The widget to apply the theme to
        theme_name: The name of the theme to apply
        font_family: The font family to use
        font_size: The font size to use
        style_params: Optional dictionary of style parameters to customize the theme
    """
    # Create palette
    palette = create_palette_from_scheme(theme_name)
    widget.setPalette(palette)

    # Apply stylesheet
    stylesheet = get_stylesheet_for_scheme(theme_name, font_family, font_size, style_params)

    # Check for special buttons in trainable segmentation page
    special_buttons = [
        'draw_label_button',
        'erase_label_button',
        'clear_all_labels_button',
        'sam_magic_wand_button',
        'sam_magic_wand_point_button',
        'sam_magic_wand_neg_point_button',
        'accept_sam_button',
        'reject_sam_button'
    ]

    # Get the object name to check if it's a special button
    object_name = widget.objectName() if hasattr(widget, 'objectName') else ""

    # If this is a special button, apply a more specific style
    if object_name in special_buttons or (hasattr(widget, 'text') and any(btn_name.replace('_', ' ').title() in widget.text() for btn_name in special_buttons)):
        # Get the scheme colors
        scheme = COLOR_SCHEMES[theme_name] if theme_name in COLOR_SCHEMES else COLOR_SCHEMES["default-dark" if "dark" in theme_name.lower() else "default-light"]

        # Get style parameters
        if style_params is None:
            style_params = UI_STYLE_PARAMS.copy()

        button_radius = style_params.get("button-radius", 6)
        padding = style_params.get("padding", 6)
        control_height = style_params.get("control-height", 28)

        # Override the stylesheet for special buttons with modern styling
        stylesheet = f"""
        QPushButton {{
            background-color: {scheme["button"].name()};
            color: {scheme["button-text"].name()};
            border: 1px solid {scheme["alternate-base"].name()};
            border-radius: {button_radius}px;
            padding: {padding}px {padding*2}px;
            min-height: {control_height}px;
            text-align: center;
        }}

        QPushButton:hover {{
            background-color: {scheme["button"].lighter(110).name()};
            border: 1px solid {scheme["alternate-base"].lighter(110).name()};
        }}

        QPushButton:pressed {{
            background-color: {scheme["button"].darker(110).name()};
            padding: {padding+1}px {padding*2-1}px {padding-1}px {padding*2+1}px; /* Slight shift effect */
        }}

        QPushButton:checked {{
            background-color: {scheme["highlight"].name()};
            color: {scheme["highlight-text"].name()};
            border: 1px solid {scheme["highlight"].darker(110).name()};
        }}
        """

    widget.setStyleSheet(stylesheet)

    # Set application font
    font = QFont(font_family, font_size)
    widget.setFont(font)

    return True



